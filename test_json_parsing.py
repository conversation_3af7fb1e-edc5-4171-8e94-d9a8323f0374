#!/usr/bin/env python3
"""
Test JSON parsing với các trường hợp lỗi thực tế
"""

import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_json_parsing():
    """Test JSON parsing với response thực tế từ log"""
    
    # Test case từ log thực tế - JSON bị cắt
    problematic_json = '''```json
[
  {
    "slideId": "p1",
    "elements": [
      {
        "objectId": "p1_i1559",
        "text": "Bài 3: NGUYÊN TỐ HÓA HỌC",
        "Type": "LessonName",
        "max_length": 60
      },
      {
        "objectId": "p1_i1560",
        "text": "Bài học này giới thiệu về khái niệm nguyên tố hóa học, số hi<PERSON><PERSON> nguyên tử, s<PERSON> khố<PERSON>, k<PERSON> hiệu nguyên tử, đồng vị và nguyên tử khối trung bình.",
        "Type": "LessonDescription",
        "max_length": 90
      }
    ]
  },
  {
    "slideId": "p5",
    "elements": [
      {
        "objectId": "p5_i1647",
        "text": "I. NGUYÊN TỐ HÓA HỌC",
        "Type": "TitleName",
        "max_length": 15
      },
      {
        "objectId": "p5_i1649",
        "text": "Nội dung chính:\\nNguyên tố hóa học là gì? Tìm hiểu về khái niệm và ví dụ minh họa.",
        "Type": "TitleContent",
        "max_length": 265
      }
    ]
  },
  {
    "slideId": "g36e9e0ba4ea_0_24",
    "elements": [
      {
        "objectId": "g36e9e0ba4ea_0_25",'''

    print("Testing JSON parsing with problematic response...")
    print("=" * 60)
    
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        print("1. Original JSON:")
        print(problematic_json[:200] + "...")
        print()
        
        print("2. Cleaning response...")
        cleaned = service._clean_json_response(problematic_json)
        print(f"Cleaned length: {len(cleaned)}")
        print("Cleaned preview:", cleaned[:200] + "...")
        print()
        
        print("3. Fixing syntax...")
        fixed = service._fix_json_syntax(cleaned)
        print(f"Fixed length: {len(fixed)}")
        print("Fixed preview:", fixed[:200] + "...")
        print("Fixed full JSON:")
        print(fixed)
        print()
        
        print("4. Attempting to parse...")
        try:
            parsed = json.loads(fixed)
            print(f"✅ SUCCESS! Parsed {len(parsed)} slides")
            for i, slide in enumerate(parsed):
                print(f"  Slide {i+1}: {slide.get('slideId')} with {len(slide.get('elements', []))} elements")
        except json.JSONDecodeError as e:
            print(f"❌ Standard fix failed: {e}")
            print(f"Error at position {e.pos}")
            
            print("\n5. Trying ultra fix...")
            ultra_fixed = service._ultra_fix_json(fixed)
            print(f"Ultra fixed length: {len(ultra_fixed)}")
            print("Ultra fixed preview:", ultra_fixed[:200] + "...")
            
            try:
                parsed = json.loads(ultra_fixed)
                print(f"✅ ULTRA FIX SUCCESS! Parsed {len(parsed)} slides")
                for i, slide in enumerate(parsed):
                    print(f"  Slide {i+1}: {slide.get('slideId')} with {len(slide.get('elements', []))} elements")
            except json.JSONDecodeError as e2:
                print(f"❌ Ultra fix also failed: {e2}")
                print(f"Error at position {e2.pos}")
                
                # Show the problematic area
                if e2.pos < len(ultra_fixed):
                    start = max(0, e2.pos - 50)
                    end = min(len(ultra_fixed), e2.pos + 50)
                    print(f"Problematic area: '{ultra_fixed[start:end]}'")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_json_parsing()

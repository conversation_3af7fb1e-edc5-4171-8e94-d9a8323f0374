"""
Test script để kiểm tra exact slide matching và thứ tự đúng
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_exact_slide_matching():
    """Test exact slide matching logic"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Mock parsed content với slide summaries có counts chính xác
        parsed_content = {
            "parsed_data": {
                "LessonName": [{"content": "Bài 1: Nguyên tố hóa học", "original_line": "..."}],
                "LessonDescription": [{"content": "Bài này giới thiệu về khái niệm nguyên tố hóa học", "original_line": "..."}],
                "CreatedDate": [{"content": "<PERSON><PERSON><PERSON> thuyết trình: 12-07-2025", "original_line": "..."}],
                "TitleName": [
                    {"content": "<PERSON>. Kh<PERSON>i niệm nguyên tố", "original_line": "..."},
                    {"content": "II. Số hiệu nguyên tử", "original_line": "..."}
                ],
                "TitleContent": [
                    {"content": "Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton", "original_line": "..."},
                    {"content": "Số hiệu nguyên tử Z bằng số proton trong hạt nhân", "original_line": "..."}
                ],
                "SubtitleName": [
                    {"content": "1. Định nghĩa", "original_line": "..."},
                    {"content": "2. Tính chất", "original_line": "..."}
                ],
                "SubtitleContent": [
                    {"content": "Nguyên tố được định nghĩa dựa trên số proton", "original_line": "..."},
                    {"content": "Tính chất hóa học được quyết định bởi số electron", "original_line": "..."}
                ],
                "ImageName": [{"content": "Hình ảnh: Bảng tuần hoàn", "original_line": "..."}],
                "ImageContent": [{"content": "Bảng tuần hoàn sắp xếp các nguyên tố", "original_line": "..."}]
            },
            "slide_summaries": [
                {
                    "slide_number": 1,
                    "placeholders": ["LessonName", "LessonDescription", "CreatedDate"],
                    "placeholder_counts": {"LessonName": 1, "LessonDescription": 1, "CreatedDate": 1}
                },
                {
                    "slide_number": 2,
                    "placeholders": ["TitleName", "TitleContent"],
                    "placeholder_counts": {"TitleName": 1, "TitleContent": 1}
                },
                {
                    "slide_number": 3,
                    "placeholders": ["SubtitleName", "SubtitleContent"],
                    "placeholder_counts": {"SubtitleName": 2, "SubtitleContent": 2}
                },
                {
                    "slide_number": 4,
                    "placeholders": ["ImageName", "ImageContent"],
                    "placeholder_counts": {"ImageName": 1, "ImageContent": 1}
                },
                {
                    "slide_number": 5,
                    "placeholders": ["TitleName", "TitleContent"],  # Duplicate type
                    "placeholder_counts": {"TitleName": 1, "TitleContent": 1}
                }
            ],
            "total_items": 12
        }
        
        # Mock analyzed template với slides có placeholder counts chính xác
        analyzed_template = {
            "slides": [
                {
                    "slideId": "intro_slide",
                    "description": "Slide giới thiệu",
                    "elements": [
                        {"objectId": "obj_1", "Type": "LessonName", "max_length": 50},
                        {"objectId": "obj_2", "Type": "LessonDescription", "max_length": 100},
                        {"objectId": "obj_3", "Type": "CreatedDate", "max_length": 30}
                    ]
                },
                {
                    "slideId": "title_slide",
                    "description": "Slide tiêu đề",
                    "elements": [
                        {"objectId": "obj_4", "Type": "TitleName", "max_length": 40},
                        {"objectId": "obj_5", "Type": "TitleContent", "max_length": 200}
                    ]
                },
                {
                    "slideId": "subtitle_slide",
                    "description": "Slide chi tiết với 2 subtitles",
                    "elements": [
                        {"objectId": "obj_6", "Type": "SubtitleName", "max_length": 30},
                        {"objectId": "obj_7", "Type": "SubtitleContent", "max_length": 150},
                        {"objectId": "obj_8", "Type": "SubtitleName", "max_length": 30},
                        {"objectId": "obj_9", "Type": "SubtitleContent", "max_length": 150}
                    ]
                },
                {
                    "slideId": "image_slide",
                    "description": "Slide hình ảnh",
                    "elements": [
                        {"objectId": "obj_10", "Type": "ImageName", "max_length": 60},
                        {"objectId": "obj_11", "Type": "ImageContent", "max_length": 120}
                    ]
                },
                {
                    "slideId": "wrong_slide",
                    "description": "Slide không match (3 TitleName)",
                    "elements": [
                        {"objectId": "obj_12", "Type": "TitleName", "max_length": 40},
                        {"objectId": "obj_13", "Type": "TitleName", "max_length": 40},
                        {"objectId": "obj_14", "Type": "TitleName", "max_length": 40}
                    ]
                }
            ]
        }
        
        logger.info("🧪 Testing EXACT slide matching...")
        logger.info(f"📊 Input: {len(parsed_content['slide_summaries'])} slide summaries")
        logger.info(f"📊 Template: {len(analyzed_template['slides'])} template slides")
        
        # Test mapping
        result = await service._map_parsed_content_to_slides(parsed_content, analyzed_template)
        
        if result["success"]:
            mapped_slides = result["slides"]
            logger.info(f"✅ Mapping successful!")
            logger.info(f"📊 Slides mapped: {len(mapped_slides)}")
            
            # Check exact matching results
            expected_mappings = [
                {"slide_number": 1, "expected_slide_id": "intro_slide"},
                {"slide_number": 2, "expected_slide_id": "title_slide"},
                {"slide_number": 3, "expected_slide_id": "subtitle_slide"},
                {"slide_number": 4, "expected_slide_id": "image_slide"},
                # slide_number 5 should NOT be mapped (no second title_slide available)
            ]
            
            # Verify exact matches
            exact_matches = 0
            correct_order = True
            
            for i, slide in enumerate(mapped_slides):
                slide_id = slide.get("slideId")
                slide_order = slide.get("slide_order", i+1)
                elements = slide.get("elements", [])
                
                logger.info(f"  Slide {slide_order}: {slide_id}")
                logger.info(f"    📋 Elements: {len(elements)}")
                
                # Check if this matches expected mapping
                for expected in expected_mappings:
                    if expected["slide_number"] == slide_order and expected["expected_slide_id"] == slide_id:
                        exact_matches += 1
                        logger.info(f"    ✅ EXACT MATCH for slide {slide_order}")
                        break
                else:
                    logger.warning(f"    ❌ Unexpected mapping for slide {slide_order}: {slide_id}")
                
                # Check order
                if i > 0 and slide_order < mapped_slides[i-1].get("slide_order", 0):
                    correct_order = False
                    logger.warning(f"    ❌ ORDER ISSUE: slide {slide_order} after slide {mapped_slides[i-1].get('slide_order')}")
            
            # Summary
            logger.info(f"📊 EXACT MATCHES: {exact_matches}/{len(expected_mappings)}")
            logger.info(f"📊 CORRECT ORDER: {'YES' if correct_order else 'NO'}")
            
            # Check that slide 5 was NOT mapped (no available template)
            slide_5_mapped = any(slide.get("slide_order") == 5 for slide in mapped_slides)
            if not slide_5_mapped:
                logger.info("✅ Slide 5 correctly NOT mapped (no available template)")
            else:
                logger.warning("❌ Slide 5 was mapped when it shouldn't be")
            
            # Success criteria
            success_criteria = [
                exact_matches >= 4,  # At least 4 exact matches
                correct_order,       # Correct order maintained
                not slide_5_mapped   # Slide 5 not mapped
            ]
            
            if all(success_criteria):
                logger.info("🎉 SUCCESS! Exact matching and ordering working correctly")
                return True
            else:
                logger.warning("⚠️ Some criteria not met")
                return False
        else:
            logger.error(f"❌ Mapping failed: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🚀 TESTING EXACT SLIDE MATCHING")
    logger.info("=" * 60)
    
    success = await test_exact_slide_matching()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"🎯 Exact Slide Matching: {'PASSED' if success else 'FAILED'}")
    
    if success:
        logger.info("🎉 Exact slide matching is working correctly!")
        logger.info("✅ Only selects slides with EXACT placeholder match")
        logger.info("✅ Maintains correct order from LLM")
        logger.info("✅ No fallback to poor matches")
        logger.info("✅ Skips slides when no exact match available")
    else:
        logger.info("❌ Exact slide matching has issues - needs debugging")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

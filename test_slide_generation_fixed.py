#!/usr/bin/env python3
"""
Test script cho slide generation với các fix:
1. <PERSON><PERSON><PERSON> to<PERSON> kích thước ch<PERSON>h xác hơn
2. Prompt linh hoạt hơn
3. <PERSON><PERSON> lý ký hiệu khoa học
4. Xóa slide thừa
"""

import asyncio
import json
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.slide_generation_service import SlideGenerationService
from app.services.textbook_retrieval_service import TextbookRetrievalService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_fixed_slide_generation():
    """Test slide generation với các fix mới"""
    print("🧪 TESTING FIXED SLIDE GENERATION")
    print("=" * 60)
    
    # Test data với nội dung hóa học có ký hiệu
    payload = {
        "lesson_id": "1",
        "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
        "config_prompt": """
Tạo slide giáo dục chuyên nghiệp với yêu cầu:
1. Tuân thủ NGHIÊM NGẶT giới hạn ký tự của từng element
2. Sử dụng ký hiệu hóa học chính xác (Unicode subscript/superscript)
3. Chỉ sử dụng slides thực sự cần thiết
4. Nội dung ngắn gọn, dễ hiểu
5. Tránh tuyệt đối việc vỡ layout
        """.strip(),
        "presentation_title": "Cấu Trúc Nguyên Tử - Hóa Học 10"
    }
    
    print(f"📝 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        # Initialize services
        print("🔧 Initializing services...")
        slide_service = SlideGenerationService()
        lesson_service = TextbookRetrievalService()
        
        # Check service availability
        if not slide_service.is_available():
            print("❌ Slide generation service not available")
            return
            
        print("✅ Services initialized successfully")
        print()
        
        # Test 1: Get lesson content
        print("📚 Step 1: Getting lesson content...")
        try:
            lesson_result = await lesson_service.get_lesson_content(payload["lesson_id"])
            lesson_content = lesson_result.get("lesson_content", "")
        except:
            lesson_content = ""
            
        if not lesson_content:
            print(f"⚠️ Using fallback chemistry content")
            # Fallback content với ký hiệu hóa học
            lesson_content = """
            Cấu Trúc Nguyên Tử
            
            1. Thành phần nguyên tử
            - Nguyên tử gồm 3 loại hạt cơ bản: electron (e⁻), proton (p⁺), neutron (n⁰)
            - Electron mang điện tích âm (-1e), khối lượng ≈ 0 amu
            - Proton mang điện tích dương (+1e), khối lượng ≈ 1 amu  
            - Neutron không mang điện, khối lượng ≈ 1 amu
            
            2. Số hiệu nguyên tử và số khối
            - Số hiệu nguyên tử (Z): Số proton trong hạt nhân
            - Số khối (A): Tổng số proton và neutron
            - Ký hiệu: ᴬZX (ví dụ: ¹²₆C, ¹⁶₈O, ²³₁₁Na)
            
            3. Đồng vị
            - Các nguyên tử có cùng Z nhưng khác A
            - Ví dụ: ¹²C₆, ¹³C₆, ¹⁴C₆
            - Tính chất hóa học giống nhau
            
            4. Cấu hình electron
            - Electron chuyển động quanh hạt nhân theo lớp
            - Lớp K (n=1): tối đa 2e⁻
            - Lớp L (n=2): tối đa 8e⁻
            - Lớp M (n=3): tối đa 18e⁻
            """
            
        print(f"✅ Lesson content: {len(lesson_content)} characters")
        print(f"📄 Content preview: {lesson_content[:150]}...")
        print()
        
        # Test 2: Copy and analyze template
        print("🔍 Step 2: Copy and analyze template...")
        copy_result = await slide_service.slides_service.copy_and_analyze_template(
            payload["template_id"], 
            payload["presentation_title"]
        )
        
        if not copy_result["success"]:
            print(f"❌ Failed to copy template: {copy_result['error']}")
            return
            
        print(f"✅ Template copied and analyzed:")
        print(f"   - Presentation ID: {copy_result['copied_presentation_id']}")
        print(f"   - Title: {copy_result['presentation_title']}")
        print(f"   - Slides: {copy_result['slide_count']}")
        print()
        
        # Test 3: Show detailed size analysis
        print("🔍 Step 3: Detailed size analysis...")
        for i, slide in enumerate(copy_result['slides']):
            print(f"   Slide {i+1} (ID: {slide['slideId']}):")
            elements = slide.get('elements', [])
            if elements:
                for j, element in enumerate(elements):
                    text_preview = element.get('text', '')[:20] + "..." if len(element.get('text', '')) > 20 else element.get('text', '')
                    
                    # Get detailed size info
                    actual_size = element.get('actualSize', {})
                    text_style = element.get('textStyle', {})
                    
                    if actual_size:
                        width = actual_size.get('width', {}).get('magnitude', 0)
                        height = actual_size.get('height', {}).get('magnitude', 0)
                        width_pt = int(width / 12700) if width > 0 else 0
                        height_pt = int(height / 12700) if height > 0 else 0
                        
                        # Get font size
                        font_size = 12
                        if text_style and text_style.get('fontSize'):
                            font_size = text_style.get('fontSize', {}).get('magnitude', 12)
                        
                        # Calculate capacity with new algorithm
                        char_width = max(6, font_size * 0.6)
                        line_height = max(15, font_size * 1.2)
                        chars_per_line = max(1, int(width_pt / char_width))
                        lines_available = max(1, int(height_pt / line_height))
                        total_chars = chars_per_line * lines_available
                        safe_chars = max(10, min(int(total_chars * 0.6), 1000))
                        
                        print(f"     Element {j+1}: {element['objectId']}")
                        print(f"       Text: \"{text_preview}\"")
                        print(f"       Size: {width_pt}x{height_pt}pt, Font: {font_size}pt")
                        print(f"       Capacity: {safe_chars} chars (conservative)")
                    else:
                        print(f"     Element {j+1}: {element['objectId']} - \"{text_preview}\"")
            else:
                print("     No editable elements")
        print()
        
        # Test 4: Generate slides content with improved prompt
        print("🤖 Step 4: Generating slides content with improved LLM...")
        slides_content_result = await slide_service._generate_slides_content(
            lesson_content,
            copy_result,
            payload.get("config_prompt")
        )
        
        if not slides_content_result["success"]:
            print(f"❌ Failed to generate slides content: {slides_content_result['error']}")
            return
            
        slides_data = slides_content_result["slides"]
        print(f"✅ Slides content generated: {len(slides_data)} slides")
        
        # Show generated content with character count analysis
        for i, slide in enumerate(slides_data):
            print(f"   Generated Slide {i+1}:")
            print(f"     ID: {slide.get('slideId')}")
            print(f"     Action: {slide.get('action', 'update')}")
            updates = slide.get('updates', {})
            print(f"     Updates: {len(updates)} elements")
            for element_id, content in updates.items():
                char_count = len(content)
                content_preview = content.replace('\n', ' ')[:80] + "..." if len(content) > 80 else content.replace('\n', ' ')
                print(f"       {element_id}: \"{content_preview}\"")
                print(f"       Length: {char_count} characters")
                
                # Check for chemical symbols
                if any(symbol in content for symbol in ['₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉', '₀', '¹', '²', '³', '⁴', '⁵']):
                    print(f"       ✅ Contains proper chemical symbols")
        print()
        
        # Test 5: Update presentation and cleanup
        print("📝 Step 5: Updating presentation and cleaning up unused slides...")
        update_result = await slide_service.slides_service.update_copied_presentation_content(
            copy_result["copied_presentation_id"],
            slides_data
        )
        
        if not update_result["success"]:
            print(f"❌ Failed to update presentation: {update_result['error']}")
            return
            
        print(f"✅ Presentation updated successfully:")
        print(f"   - Slides updated: {update_result.get('slides_updated', 0)}")
        print(f"   - Slides created: {update_result.get('slides_created', 0)}")
        print(f"   - Total requests: {update_result.get('requests_executed', 0)}")
        
        # Test 6: Delete unused slides
        print("🗑️ Step 6: Cleaning up unused slides...")
        used_slide_ids = []
        for slide_data in slides_data:
            slide_id = slide_data.get('slideId')
            if slide_id and not slide_id.startswith('new_slide_'):
                used_slide_ids.append(slide_id)
        
        if used_slide_ids:
            delete_result = await slide_service.slides_service.delete_unused_slides(
                copy_result["copied_presentation_id"],
                used_slide_ids
            )
            print(f"✅ Cleanup result: {delete_result}")
        else:
            print("ℹ️ No cleanup needed - all slides are new")
        print()
        
        # Final result
        print("🎉 FIXED SLIDE GENERATION TEST COMPLETED SUCCESSFULLY!")
        print(f"📊 Final Results:")
        print(f"   - Presentation ID: {copy_result['copied_presentation_id']}")
        print(f"   - Web View Link: {copy_result['web_view_link']}")
        print(f"   - Total Slides Used: {len(slides_data)}")
        print(f"   - Improvements Applied:")
        print(f"     ✅ Conservative character limits")
        print(f"     ✅ Chemical symbols with Unicode")
        print(f"     ✅ Unused slides cleanup")
        print(f"     ✅ Flexible content formatting")
        print()
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")

if __name__ == "__main__":
    asyncio.run(test_fixed_slide_generation())

"""
Debug script để xem parent placeholder của các elements
"""

import json
import asyncio
from app.services.google_slides_service import get_google_slides_service

async def debug_parent_placeholder():
    """Debug parent placeholder chi tiết"""
    template_id = "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA"
    
    print(f"🔍 Debugging Parent Placeholder for template: {template_id}")
    print("=" * 80)
    
    # Get slides service
    slides_service = get_google_slides_service()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    try:
        # Get raw presentation data
        presentation = slides_service.slides_service.presentations().get(
            presentationId=template_id
        ).execute()
        
        slides = presentation.get('slides', [])
        if not slides:
            print("❌ No slides found")
            return
            
        # Analyze first slide elements
        first_slide = slides[0]
        elements = first_slide.get('pageElements', [])
        
        print(f"📊 SLIDE: {first_slide.get('objectId')}")
        print(f"Elements count: {len(elements)}")
        print()
        
        # Get parent object IDs from slide elements
        parent_ids = []
        for element in elements:
            if 'shape' in element and 'placeholder' in element['shape']:
                placeholder = element['shape']['placeholder']
                parent_id = placeholder.get('parentObjectId')
                if parent_id:
                    parent_ids.append({
                        'element_id': element.get('objectId'),
                        'parent_id': parent_id,
                        'placeholder_type': placeholder.get('type')
                    })
                    print(f"📋 Element {element.get('objectId')}:")
                    print(f"  Placeholder Type: {placeholder.get('type')}")
                    print(f"  Parent Object ID: {parent_id}")
                    print()
        
        # Now search for these parent placeholders in layouts and masters
        print("🔍 SEARCHING FOR PARENT PLACEHOLDERS:")
        print("=" * 60)
        
        for parent_info in parent_ids:
            parent_id = parent_info['parent_id']
            print(f"📋 Looking for Parent: {parent_id}")
            print(f"  For Element: {parent_info['element_id']}")
            print(f"  Placeholder Type: {parent_info['placeholder_type']}")
            
            found = False
            
            # Search in layouts
            print(f"  🔍 Searching in {len(presentation.get('layouts', []))} layouts...")
            for i, layout in enumerate(presentation.get('layouts', [])):
                for element in layout.get('pageElements', []):
                    if element.get('objectId') == parent_id:
                        found = True
                        print(f"  ✅ FOUND in Layout {i+1}:")
                        print(f"    Layout ID: {layout.get('objectId')}")
                        print(f"    Element ID: {element.get('objectId')}")
                        
                        if 'shape' in element and 'text' in element['shape']:
                            text_content = element['shape']['text']
                            print(f"    Has text: Yes")
                            
                            if 'textElements' in text_content:
                                for j, te in enumerate(text_content['textElements']):
                                    if 'textRun' in te:
                                        text_run = te['textRun']
                                        style = text_run.get('style', {})
                                        print(f"    TextElement {j+1}:")
                                        print(f"      Content: '{text_run.get('content', '').strip()}'")
                                        print(f"      Style: {style}")
                                        if 'fontSize' in style:
                                            print(f"      Font Size: {style['fontSize']}")
                        else:
                            print(f"    Has text: No")
                        
                        print(f"    Full element structure:")
                        print(f"    {json.dumps(element, indent=6, ensure_ascii=False)}")
                        break
                if found:
                    break
            
            # Search in masters if not found in layouts
            if not found:
                print(f"  🔍 Searching in {len(presentation.get('masters', []))} masters...")
                for i, master in enumerate(presentation.get('masters', [])):
                    for element in master.get('pageElements', []):
                        if element.get('objectId') == parent_id:
                            found = True
                            print(f"  ✅ FOUND in Master {i+1}:")
                            print(f"    Master ID: {master.get('objectId')}")
                            print(f"    Element ID: {element.get('objectId')}")
                            
                            if 'shape' in element and 'text' in element['shape']:
                                text_content = element['shape']['text']
                                print(f"    Has text: Yes")
                                
                                if 'textElements' in text_content:
                                    for j, te in enumerate(text_content['textElements']):
                                        if 'textRun' in te:
                                            text_run = te['textRun']
                                            style = text_run.get('style', {})
                                            print(f"    TextElement {j+1}:")
                                            print(f"      Content: '{text_run.get('content', '').strip()}'")
                                            print(f"      Style: {style}")
                                            if 'fontSize' in style:
                                                print(f"      Font Size: {style['fontSize']}")
                            else:
                                print(f"    Has text: No")
                            
                            print(f"    Full element structure:")
                            print(f"    {json.dumps(element, indent=6, ensure_ascii=False)}")
                            break
                    if found:
                        break
            
            if not found:
                print(f"  ❌ NOT FOUND in layouts or masters")
            
            print("-" * 60)
        
        # Save debug data
        debug_data = {
            "slide_elements": elements,
            "parent_ids": parent_ids,
            "layouts": presentation.get('layouts', []),
            "masters": presentation.get('masters', [])
        }
        
        with open('parent_placeholder_debug.json', 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, indent=2, ensure_ascii=False)
        
        print("✅ Debug data saved to 'parent_placeholder_debug.json'")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_parent_placeholder())

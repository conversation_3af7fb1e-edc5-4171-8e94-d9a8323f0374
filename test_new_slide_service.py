#!/usr/bin/env python3
"""
Test script for new slide generation service
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

def test_slide_service():
    """Test the new slide generation service"""
    try:
        print("🔄 Testing new slide generation service...")
        
        # Import service
        from app.services.slide_generation_service import get_slide_generation_service
        print("✅ Import successful")
        
        # Create service instance
        service = get_slide_generation_service()
        print("✅ Service created")
        
        print(f"Service type: {type(service)}")
        print(f"Service available: {service.is_available()}")
        
        # Test placeholder detection
        test_text = "LessonName <50>"
        placeholder_type, max_length = service._detect_placeholder_type_from_text(test_text)
        print(f"✅ Placeholder detection test: '{test_text}' -> Type: {placeholder_type}, Max: {max_length}")
        
        # Test content analysis
        test_text2 = "This is a lesson title"
        placeholder_type2, max_length2 = service._detect_placeholder_by_content(test_text2)
        print(f"✅ Content analysis test: '{test_text2}' -> Type: {placeholder_type2}, Max: {max_length2}")
        
        print("🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_slide_service()

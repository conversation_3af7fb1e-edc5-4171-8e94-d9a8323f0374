#!/usr/bin/env python3
"""
Test script for new text-based workflow
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_presentation_content_generation():
    """Test the first AI call - generate presentation content as text"""
    from app.services.slide_generation_service import get_slide_generation_service
    
    service = get_slide_generation_service()
    
    # Sample lesson content
    lesson_content = """
    Bài học về Hàm số bậc nhất
    
    Hàm số bậc nhất là một trong những khái niệm cơ bản trong toán học.
    Hàm số bậc nhất có dạng y = ax + b, trong đó a ≠ 0.
    
    Các tính chất:
    - <PERSON><PERSON> thị là đường thẳng
    - <PERSON><PERSON> số a quyết định độ dốc
    - <PERSON>ệ số b là tung độ gốc
    
    Ứng dụng trong thực tế rất phong phú.
    """
    
    print("🔄 Testing presentation content generation...")
    print(f"Input lesson content: {lesson_content[:100]}...")
    
    # Create prompt
    prompt = service._create_presentation_content_prompt(lesson_content)
    print(f"✅ Prompt created, length: {len(prompt)} characters")
    print(f"Prompt preview: {prompt[:200]}...")
    
    return True

def test_content_mapping():
    """Test the second AI call - map content to template"""
    from app.services.slide_generation_service import get_slide_generation_service
    
    service = get_slide_generation_service()
    
    # Sample presentation content (text)
    presentation_content = """
# Hàm số bậc nhất

## Tóm tắt bài học
Tìm hiểu về hàm số bậc nhất, đồ thị và ứng dụng

## I. Khái niệm cơ bản

### Nội dung chính:
Hàm số bậc nhất có dạng y = ax + b với a ≠ 0

### 1.1 Định nghĩa
Hàm số bậc nhất là hàm số có dạng y = ax + b

**Các điểm quan trọng:**
• a là hệ số góc
• b là tung độ gốc
• a ≠ 0

### 1.2 Tính chất
Đồ thị là đường thẳng

## II. Ứng dụng
Sử dụng trong nhiều lĩnh vực
"""
    
    # Sample analyzed template
    analyzed_template = {
        "slides": [
            {
                "slideId": "slide1",
                "description": "Slide dành cho 1 LessonName, 1 LessonDescription",
                "elements": [
                    {
                        "objectId": "obj1",
                        "Type": "LessonName",
                        "max_length": 50,
                        "font_size": 24,
                        "font_family": "Arial",
                        "font_style": {"bold": True, "italic": False, "underline": False}
                    },
                    {
                        "objectId": "obj2", 
                        "Type": "LessonDescription",
                        "max_length": 100,
                        "font_size": 16,
                        "font_family": "Arial",
                        "font_style": {"bold": False, "italic": False, "underline": False}
                    }
                ]
            }
        ]
    }
    
    print("\n🔄 Testing content mapping...")
    print(f"Input presentation content: {presentation_content[:100]}...")
    print(f"Template slides: {len(analyzed_template['slides'])}")
    
    # Create prompt
    prompt = service._create_content_mapping_prompt(
        presentation_content, 
        analyzed_template
    )
    print(f"✅ Mapping prompt created, length: {len(prompt)} characters")
    print(f"Prompt preview: {prompt[:200]}...")
    
    return True

def test_placeholder_detection():
    """Test placeholder detection with various formats"""
    from app.services.slide_generation_service import get_slide_generation_service
    
    service = get_slide_generation_service()
    
    test_cases = [
        "LessonName <50>",
        "TitleContent <200>", 
        "BulletItem <100>",
        "This is a lesson title",  # Fallback detection
        "• Important point",       # Fallback detection
    ]
    
    print("\n🔄 Testing placeholder detection...")
    
    for test_text in test_cases:
        placeholder_type, max_length = service._detect_placeholder_type_from_text(test_text)
        print(f"'{test_text}' -> Type: {placeholder_type}, Max: {max_length}")
    
    return True

def main():
    """Main test function"""
    print("🚀 Testing new text-based workflow...")
    print("=" * 50)
    
    # Test 1: Presentation content generation
    test1_ok = test_presentation_content_generation()
    
    # Test 2: Content mapping
    test2_ok = test_content_mapping()
    
    # Test 3: Placeholder detection
    test3_ok = test_placeholder_detection()
    
    # Summary
    print("\n📋 Test Summary:")
    print(f"  Presentation Content Generation: {'✅' if test1_ok else '❌'}")
    print(f"  Content Mapping: {'✅' if test2_ok else '❌'}")
    print(f"  Placeholder Detection: {'✅' if test3_ok else '❌'}")
    
    if all([test1_ok, test2_ok, test3_ok]):
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed")

if __name__ == "__main__":
    main()

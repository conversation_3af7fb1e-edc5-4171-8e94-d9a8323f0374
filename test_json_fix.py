#!/usr/bin/env python3
"""
Test script để kiểm tra J<PERSON><PERSON> fixing functions
"""

import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.slide_generation_service import get_slide_generation_service

def test_json_fixing():
    """Test JSON fixing functions"""
    
    # Test case 1: JSON with trailing commas
    test_json_1 = '''```json
[
  {
    "slideId": "slide1",
    "elements": [
      {
        "objectId": "obj1",
        "text": "Test",
        "Type": "LessonName",
        "max_length": 50,
      }
    ]
  },
]
```'''

    # Test case 2: JSON with missing commas
    test_json_2 = '''[
  {
    "slideId": "slide1"
    "elements": [
      {
        "objectId": "obj1"
        "text": "Test"
        "Type": "LessonName"
        "max_length": 50
      }
    ]
  }
]'''

    # Test case 3: J<PERSON><PERSON> with extra text
    test_json_3 = '''Here is the JSON response:
[
  {
    "slideId": "slide1",
    "elements": [
      {
        "objectId": "obj1",
        "text": "Test",
        "Type": "LessonName",
        "max_length": 50
      }
    ]
  }
]
This completes the mapping.'''

    service = get_slide_generation_service()
    
    test_cases = [
        ("Trailing commas", test_json_1),
        ("Missing commas", test_json_2),
        ("Extra text", test_json_3)
    ]
    
    for name, test_json in test_cases:
        print(f"\n{'='*50}")
        print(f"Testing: {name}")
        print(f"{'='*50}")
        
        print("Original JSON:")
        print(test_json[:200] + "..." if len(test_json) > 200 else test_json)
        print()
        
        # Step 1: Clean response
        cleaned = service._clean_json_response(test_json)
        print("After cleaning:")
        print(cleaned[:200] + "..." if len(cleaned) > 200 else cleaned)
        print()
        
        # Step 2: Fix syntax
        fixed = service._fix_json_syntax(cleaned)
        print("After fixing:")
        print(fixed[:200] + "..." if len(fixed) > 200 else fixed)
        print()
        
        # Step 3: Try parsing
        try:
            parsed = json.loads(fixed)
            print(f"✅ JSON parsing successful!")
            print(f"Parsed {len(parsed)} slides")
            if parsed and len(parsed) > 0:
                print(f"First slide ID: {parsed[0].get('slideId')}")
                print(f"First slide elements: {len(parsed[0].get('elements', []))}")
        except Exception as e:
            print(f"❌ JSON parsing failed: {e}")
            
            # Try ultra fix
            print("Trying ultra fix...")
            try:
                ultra_fixed = service._ultra_fix_json(fixed)
                print("Ultra fixed:")
                print(ultra_fixed[:200] + "..." if len(ultra_fixed) > 200 else ultra_fixed)
                
                parsed = json.loads(ultra_fixed)
                print(f"✅ Ultra fix successful!")
                print(f"Parsed {len(parsed)} slides")
            except Exception as e2:
                print(f"❌ Ultra fix also failed: {e2}")

if __name__ == "__main__":
    test_json_fixing()

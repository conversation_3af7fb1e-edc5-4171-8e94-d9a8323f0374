"""
Test script để kiểm tra slide summaries với số lượng rõ ràng và loại bỏ BulletItem
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_prompt_requirements():
    """Test prompt có yêu cầu mới"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        lesson_content = "Sample lesson content"
        prompt = service._create_annotated_presentation_prompt(lesson_content)
        
        # Check for new requirements
        new_requirements = [
            "5-7 slides",
            "TẠMTHỜI KHÔNG SỬ DỤNG BulletItem",
            "SỐ LƯỢNG RÕ RÀNG",
            "2xTitleName",
            "3xSubtitleContent",
            "ÍT NHẤT 5-7 SLIDES"
        ]
        
        found_requirements = []
        for req in new_requirements:
            if req in prompt:
                found_requirements.append(req)
        
        logger.info(f"📝 New requirements found: {found_requirements}")
        
        # Check that BulletItem is not in valid placeholders
        if "BulletItem" not in prompt or "TẠMTHỜI KHÔNG SỬ DỤNG BulletItem" in prompt:
            logger.info("✅ BulletItem correctly excluded")
            bullet_excluded = True
        else:
            logger.warning("⚠️ BulletItem still present in prompt")
            bullet_excluded = False
        
        if len(found_requirements) >= 4 and bullet_excluded:
            logger.info("✅ Prompt correctly includes new requirements")
            return True
        else:
            logger.warning(f"⚠️ Missing some new requirements")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        return False

def test_parsing_with_quantity_format():
    """Test parsing với format số lượng mới"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample content với format số lượng mới
        sample_content = """
Bài 1: Nguyên tố hóa học (LessonName)
Bài này giới thiệu về khái niệm nguyên tố hóa học và các tính chất cơ bản (LessonDescription)
Ngày thuyết trình: 12-07-2025 (CreatedDate)

=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================

I. Khái niệm nguyên tố hóa học (TitleName)
Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton trong hạt nhân. (TitleContent)

II. Tính chất của nguyên tố (TitleName)
Mỗi nguyên tố có tính chất hóa học riêng biệt được quyết định bởi số proton. (TitleContent)

=== SLIDE 2 SUMMARY ===
Placeholders: 2xTitleName, 2xTitleContent
===========================

Số hiệu nguyên tử (SubtitleName)
Số hiệu nguyên tử Z bằng số proton trong hạt nhân nguyên tử. (SubtitleContent)

Ký hiệu nguyên tố (SubtitleName)
Mỗi nguyên tố có ký hiệu riêng, thường là 1-2 chữ cái Latin. (SubtitleContent)

Phân loại nguyên tố (SubtitleName)
Nguyên tố được phân loại thành kim loại, phi kim và á kim. (SubtitleContent)

=== SLIDE 3 SUMMARY ===
Placeholders: 3xSubtitleName, 3xSubtitleContent
===========================

Hình ảnh minh họa: Bảng tuần hoàn các nguyên tố (ImageName)
Bảng tuần hoàn sắp xếp các nguyên tố theo số hiệu nguyên tử tăng dần (ImageContent)

=== SLIDE 4 SUMMARY ===
Placeholders: 1xImageName, 1xImageContent
===========================
"""
        
        logger.info("🧪 Testing parsing with quantity format...")
        
        parsed_result = service._parse_annotated_content(sample_content)
        
        if parsed_result:
            slide_summaries = parsed_result.get("slide_summaries", [])
            logger.info(f"📊 Slide summaries found: {len(slide_summaries)}")
            
            # Check each slide summary
            for i, summary in enumerate(slide_summaries):
                slide_num = summary.get("slide_number")
                placeholders = summary.get("placeholders", [])
                placeholder_counts = summary.get("placeholder_counts", {})
                
                logger.info(f"  Slide {slide_num}:")
                logger.info(f"    Placeholders: {placeholders}")
                logger.info(f"    Counts: {placeholder_counts}")
            
            # Verify specific counts
            if len(slide_summaries) >= 4:
                # Check slide 2 should have 2xTitleName, 2xTitleContent
                slide_2 = slide_summaries[1]  # Index 1 for slide 2
                counts_2 = slide_2.get("placeholder_counts", {})
                
                if counts_2.get("TitleName") == 2 and counts_2.get("TitleContent") == 2:
                    logger.info("✅ Slide 2 counts correctly parsed")
                    
                # Check slide 3 should have 3xSubtitleName, 3xSubtitleContent
                slide_3 = slide_summaries[2]  # Index 2 for slide 3
                counts_3 = slide_3.get("placeholder_counts", {})
                
                if counts_3.get("SubtitleName") == 3 and counts_3.get("SubtitleContent") == 3:
                    logger.info("✅ Slide 3 counts correctly parsed")
                    return True
                else:
                    logger.warning(f"⚠️ Slide 3 counts incorrect: {counts_3}")
                    return False
            else:
                logger.warning(f"⚠️ Expected 4+ slides, got {len(slide_summaries)}")
                return False
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bulletitem_exclusion():
    """Test BulletItem đã được loại bỏ"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample content có BulletItem (should be ignored)
        sample_content = """
Bài 1: Test (LessonName)
Mô tả test (LessonDescription)

Các điểm quan trọng:
• Điểm 1 (BulletItem)
• Điểm 2 (BulletItem)

Tiêu đề chính (TitleName)
Nội dung chính (TitleContent)
"""
        
        logger.info("🧪 Testing BulletItem exclusion...")
        
        parsed_result = service._parse_annotated_content(sample_content)
        
        if parsed_result:
            parsed_data = parsed_result.get("parsed_data", {})
            
            # Check that BulletItem is not in parsed_data or is empty
            if "BulletItem" not in parsed_data:
                logger.info("✅ BulletItem correctly excluded from parsed_data")
                bullet_excluded = True
            elif len(parsed_data.get("BulletItem", [])) == 0:
                logger.info("✅ BulletItem present but empty (correctly ignored)")
                bullet_excluded = True
            else:
                logger.warning(f"⚠️ BulletItem still being parsed: {parsed_data.get('BulletItem', [])}")
                bullet_excluded = False
            
            # Check other placeholders are still working
            other_types = ["LessonName", "LessonDescription", "TitleName", "TitleContent"]
            working_types = []
            for ptype in other_types:
                if ptype in parsed_data and len(parsed_data[ptype]) > 0:
                    working_types.append(ptype)
            
            logger.info(f"📊 Working placeholder types: {working_types}")
            
            if bullet_excluded and len(working_types) >= 3:
                logger.info("✅ BulletItem excluded while other types work correctly")
                return True
            else:
                logger.warning("⚠️ Issues with placeholder type handling")
                return False
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("🚀 TESTING IMPROVED SLIDE SUMMARIES")
    logger.info("=" * 60)
    
    # Test 1: Prompt requirements
    logger.info("\n📝 TEST 1: New Prompt Requirements")
    prompt_success = test_prompt_requirements()
    
    # Test 2: Quantity format parsing
    logger.info("\n🔢 TEST 2: Quantity Format Parsing")
    quantity_success = test_parsing_with_quantity_format()
    
    # Test 3: BulletItem exclusion
    logger.info("\n🚫 TEST 3: BulletItem Exclusion")
    exclusion_success = test_bulletitem_exclusion()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"📝 New Prompt Requirements: {'PASSED' if prompt_success else 'FAILED'}")
    logger.info(f"🔢 Quantity Format Parsing: {'PASSED' if quantity_success else 'FAILED'}")
    logger.info(f"🚫 BulletItem Exclusion: {'PASSED' if exclusion_success else 'FAILED'}")
    
    if prompt_success and quantity_success and exclusion_success:
        logger.info("🎉 All improvements working correctly!")
        logger.info("✅ AI will generate 5-7 detailed slides")
        logger.info("✅ Slide summaries include quantity information")
        logger.info("✅ BulletItem temporarily excluded")
        logger.info("✅ System ready for more detailed presentations")
    else:
        logger.info("❌ Some improvements have issues - needs debugging")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()

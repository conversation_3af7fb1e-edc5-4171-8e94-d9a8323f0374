{"slide_elements": [{"objectId": "gc6f919934_0_1", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407, "scaleY": 0.3112, "translateX": 390525, "translateY": 1819275, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 13, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 13, "textRun": {"content": "Lesson Title\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "CENTERED_TITLE", "parentObjectId": "gc6f919934_0_70"}}}, {"objectId": "gc6f919934_0_2", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.14429999887943268, "translateX": 390525, "translateY": 2789130.2734375, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 13, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 13, "textRun": {"content": "Created Date\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SUBTITLE", "parentObjectId": "gc6f919934_0_71"}}}], "parent_ids": [{"element_id": "gc6f919934_0_1", "parent_id": "gc6f919934_0_70", "placeholder_type": "CENTERED_TITLE"}, {"element_id": "gc6f919934_0_2", "parent_id": "gc6f919934_0_71", "placeholder_type": "SUBTITLE"}], "layouts": [{"objectId": "gc6f919934_0_67", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_68", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": -0.29919999837875366, "scaleY": 0.29919999837875366, "translateX": 9144000, "translateY": 4245925, "unit": "EMU"}, "shape": {"shapeType": "RIGHT_TRIANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "LIGHT1"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_69", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": -0.29919999837875366, "scaleY": 0.29919999837875366, "translateX": 9144000, "translateY": 4245875, "unit": "EMU"}, "shape": {"shapeType": "ROUND_1_RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "LIGHT1"}, "alpha": 0.6808}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_70", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.31119999289512634, "translateX": 390525, "translateY": 1819275, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 48, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "CENTERED_TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_71", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.14429999887943268, "translateX": 390525, "translateY": 2789130.2734375, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 18, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SUBTITLE", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_72", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "TITLE", "displayName": "<PERSON>rang trình bày chứa tiêu đề"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_73", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_74", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.3375999927520752, "translateX": 460950, "translateY": 2065350, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 42, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "contentAlignment": "MIDDLE", "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_75", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "SECTION_HEADER", "displayName": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> mục"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_76", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_77", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": -1.152500033378601, "translateY": 5143500, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_78", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": 0.03620000183582306, "translateY": 1686000, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_79", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.25589999556541443, "translateX": 471900, "translateY": 738725, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {}}, "1": {"bulletStyle": {}}, "2": {"bulletStyle": {}}, "3": {"bulletStyle": {}}, "4": {"bulletStyle": {}}, "5": {"bulletStyle": {}}, "6": {"bulletStyle": {}}, "7": {"bulletStyle": {}}, "8": {"bulletStyle": {}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_80", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.9034000039100647, "translateX": 471900, "translateY": 1919075, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {}}, "1": {"bulletStyle": {}}, "2": {"bulletStyle": {}}, "3": {"bulletStyle": {}}, "4": {"bulletStyle": {}}, "5": {"bulletStyle": {}}, "6": {"bulletStyle": {}}, "7": {"bulletStyle": {}}, "8": {"bulletStyle": {}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_81", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "TITLE_AND_BODY", "displayName": "Ti<PERSON>u đề và nội dung"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_82", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_83", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": -1.152500033378601, "translateY": 5143500, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_84", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": 0.03620000183582306, "translateY": 1686000, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_85", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.25589999556541443, "translateX": 471900, "translateY": 738725, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {}}, "1": {"bulletStyle": {}}, "2": {"bulletStyle": {}}, "3": {"bulletStyle": {}}, "4": {"bulletStyle": {}}, "5": {"bulletStyle": {}}, "6": {"bulletStyle": {}}, "7": {"bulletStyle": {}}, "8": {"bulletStyle": {}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_86", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.333299994468689, "scaleY": 0.9034000039100647, "translateX": 471900, "translateY": 1919075.390625, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 14, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 14, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_87", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.333299994468689, "scaleY": 0.9034000039100647, "translateX": 4694250, "translateY": 1919075.390625, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 14, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 14, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 12, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "index": 1, "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_88", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "TITLE_AND_TWO_COLUMNS", "displayName": "Ti<PERSON>u đề và hai cột"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_89", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_90", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": -1.4957000017166138, "translateY": 5143500, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_91", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": 0.03620000183582306, "translateY": 656350, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_92", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.942199945449829, "scaleY": 0.20090000331401825, "translateX": 98250, "translateY": 16350, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 18, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "contentAlignment": "MIDDLE", "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_93", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "TITLE_ONLY", "displayName": "Chỉ tiêu đề"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_94", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_95", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.9558000564575195, "scaleY": -1.7144999504089355, "translateX": 3276600, "translateY": 5143525, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_96", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"shearX": 0.03620000183582306, "shearY": -1.7144999504089355, "translateX": 3276600, "translateY": 5143500, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_97", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.9359999895095825, "scaleY": 0.31779998540878296, "translateX": 226077.63671875, "translateY": 357800, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 24, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_98", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.9359999895095825, "scaleY": 1.0544999837875366, "translateX": 226075, "translateY": 1465800, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_99", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "ONE_COLUMN_TEXT", "displayName": "<PERSON><PERSON><PERSON> bản trong một cột"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_100", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_101", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.075700044631958, "scaleY": 1.3636000156402588, "translateX": 490250, "translateY": 488250, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 60, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "contentAlignment": "MIDDLE", "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_102", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "MAIN_POINT", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_103", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_104", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": -1.5240000486373901, "scaleY": 1.7144999504089355, "translateX": 4572000, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_105", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"shearX": -0.03620000183582306, "shearY": 1.7143000364303589, "translateX": 4572175, "translateY": 600, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_106", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.3483999967575073, "scaleY": 0.49410000443458557, "translateX": 265500, "translateY": 1233175, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 42, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_107", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.3483999967575073, "scaleY": 0.4117000102996826, "translateX": 265500, "translateY": 2779466.9921875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "CENTER", "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "1": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "2": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "3": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "4": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "5": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "6": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "7": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}, "8": {"bulletStyle": {"fontSize": {"magnitude": 21, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SUBTITLE", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_108", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 1.2790000438690186, "scaleY": 1.2316999435424805, "translateX": 4939500, "translateY": 724200, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "contentAlignment": "MIDDLE", "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_109", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "SECTION_TITLE_AND_DESCRIPTION", "displayName": "Ti<PERSON>u đề mục và mô tả"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_110", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_111", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": -1.5652999877929688, "translateY": 4695900, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "shapeProperties": {"shapeBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_112", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 3.0480000972747803, "scaleY": -0.024700000882148743, "translateY": 4696825, "unit": "EMU"}, "shape": {"shapeType": "RECTANGLE", "shapeProperties": {"shapeBackgroundFill": {}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}}}, {"objectId": "gc6f919934_0_113", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7939999103546143, "scaleY": 0.14890000224113464, "translateX": 57150, "translateY": 4696825, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "spaceBelow": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "fontSize": {"magnitude": 12, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "contentAlignment": "MIDDLE", "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_114", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "CAPTION_ONLY", "displayName": "<PERSON><PERSON> th<PERSON>ch"}, "pageProperties": {"pageBackgroundFill": {"propertyState": "INHERIT"}}}, {"objectId": "gc6f919934_0_115", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_116", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.6545000076293945, "translateX": 475500, "translateY": 1258525, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "1": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "2": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "3": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "4": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "5": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "6": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "7": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}, "8": {"bulletStyle": {"foregroundColor": {"opaqueColor": {"themeColor": "DARK2"}}, "fontSize": {"magnitude": 120, "unit": "PT"}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "TITLE", "parentObjectId": "gc6f919934_0_64"}}}, {"objectId": "gc6f919934_0_117", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.4336000084877014, "translateX": 475500, "translateY": 3304625, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"alignment": "CENTER", "direction": "LEFT_TO_RIGHT"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {}}, "1": {"bulletStyle": {}}, "2": {"bulletStyle": {}}, "3": {"bulletStyle": {}}, "4": {"bulletStyle": {}}, "5": {"bulletStyle": {}}, "6": {"bulletStyle": {}}, "7": {"bulletStyle": {}}, "8": {"bulletStyle": {}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "BODY", "parentObjectId": "gc6f919934_0_65"}}}, {"objectId": "gc6f919934_0_118", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "BIG_NUMBER", "displayName": "Số lớn"}, "pageProperties": {"pageBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}}}}}, {"objectId": "gc6f919934_0_119", "pageType": "LAYOUT", "pageElements": [{"objectId": "gc6f919934_0_120", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"direction": "LEFT_TO_RIGHT"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "INHERIT"}, "outline": {"propertyState": "INHERIT"}, "shadow": {"propertyState": "INHERIT"}, "autofit": {"fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER", "parentObjectId": "gc6f919934_0_66"}}}], "layoutProperties": {"masterObjectId": "gc6f919934_0_63", "name": "BLANK", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, "pageProperties": {"pageBackgroundFill": {"solidFill": {"color": {"themeColor": "ACCENT4"}}}}}], "masters": [{"objectId": "gc6f919934_0_63", "pageType": "MASTER", "pageElements": [{"objectId": "gc6f919934_0_64", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.25589999556541443, "translateX": 471900, "translateY": 738725, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": " ", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": " ", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "1": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "2": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "3": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "4": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "5": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "6": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "7": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "8": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT1"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 32, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "BOTTOM", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "TITLE"}}}, {"objectId": "gc6f919934_0_65", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 2.7407000064849854, "scaleY": 0.9034000039100647, "translateX": 471900, "translateY": 1919075, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 1, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "glyph": "●", "bulletStyle": {}}}}, {"endIndex": 1, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 18, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 1, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 2, "endIndex": 3, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 2, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 2, "endIndex": 3, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 3, "endIndex": 4, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 3, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 3, "endIndex": 4, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 4, "endIndex": 5, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 4, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 4, "endIndex": 5, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 5, "endIndex": 6, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 5, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 5, "endIndex": 6, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 6, "endIndex": 7, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 6, "glyph": "●", "bulletStyle": {}}}}, {"startIndex": 6, "endIndex": 7, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 7, "endIndex": 8, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 7, "glyph": "○", "bulletStyle": {}}}}, {"startIndex": 7, "endIndex": 8, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 8, "endIndex": 9, "paragraphMarker": {"style": {"lineSpacing": 115, "alignment": "START", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"magnitude": 16, "unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}, "bullet": {"listId": "bodyPlaceholderListEntity", "nestingLevel": 8, "glyph": "■", "bulletStyle": {}}}}, {"startIndex": 8, "endIndex": 9, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}], "lists": {"bodyPlaceholderListEntity": {"listId": "bodyPlaceholderListEntity", "nestingLevel": {"0": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 18, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "1": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "2": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "3": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "4": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "5": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "6": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "7": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}, "8": {"bulletStyle": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 14, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}}}}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "TOP", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "BODY"}}}, {"objectId": "gc6f919934_0_66", "size": {"width": {"magnitude": 3000000, "unit": "EMU"}, "height": {"magnitude": 3000000, "unit": "EMU"}}, "transform": {"scaleX": 0.18289999663829803, "scaleY": 0.13120000064373016, "translateX": 8523541.40625, "translateY": 4695623.046875, "unit": "EMU"}, "shape": {"shapeType": "TEXT_BOX", "text": {"textElements": [{"endIndex": 2, "paragraphMarker": {"style": {"lineSpacing": 100, "alignment": "END", "indentStart": {"unit": "PT"}, "indentEnd": {"unit": "PT"}, "spaceAbove": {"unit": "PT"}, "spaceBelow": {"unit": "PT"}, "indentFirstLine": {"unit": "PT"}, "direction": "LEFT_TO_RIGHT", "spacingMode": "NEVER_COLLAPSE"}}}, {"endIndex": 1, "autoText": {"type": "SLIDE_NUMBER", "content": "#", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 10, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}, {"startIndex": 1, "endIndex": 2, "textRun": {"content": "\n", "style": {"backgroundColor": {}, "foregroundColor": {"opaqueColor": {"themeColor": "LIGHT2"}}, "bold": false, "italic": false, "fontFamily": "Roboto", "fontSize": {"magnitude": 10, "unit": "PT"}, "baselineOffset": "NONE", "smallCaps": false, "strikethrough": false, "underline": false, "weightedFontFamily": {"fontFamily": "Roboto", "weight": 400}}}}]}, "shapeProperties": {"shapeBackgroundFill": {"propertyState": "NOT_RENDERED", "solidFill": {"color": {"rgbColor": {"red": 1, "green": 1, "blue": 1}}, "alpha": 1}}, "outline": {"outlineFill": {"solidFill": {"color": {"rgbColor": {}}, "alpha": 1}}, "weight": {"magnitude": 9525, "unit": "EMU"}, "dashStyle": "SOLID", "propertyState": "NOT_RENDERED"}, "shadow": {"type": "OUTER", "transform": {"scaleX": 1, "scaleY": 1, "unit": "EMU"}, "alignment": "BOTTOM_LEFT", "blurRadius": {"unit": "EMU"}, "color": {"rgbColor": {}}, "alpha": 1, "rotateWithShape": false, "propertyState": "NOT_RENDERED"}, "contentAlignment": "MIDDLE", "autofit": {"autofitType": "NONE", "fontScale": 1}}, "placeholder": {"type": "SLIDE_NUMBER"}}}], "pageProperties": {"pageBackgroundFill": {"solidFill": {"color": {"themeColor": "DARK1"}, "alpha": 1}}, "colorScheme": {"colors": [{"type": "DARK1", "color": {"red": 0.25882354, "green": 0.52156866, "blue": 0.95686275}}, {"type": "LIGHT1", "color": {"red": 1, "green": 1, "blue": 1}}, {"type": "DARK2", "color": {"red": 0.25882354, "green": 0.25882354, "blue": 0.25882354}}, {"type": "LIGHT2", "color": {"red": 0.4509804, "green": 0.4509804, "blue": 0.4509804}}, {"type": "ACCENT1", "color": {"red": 0.007843138, "green": 0.46666667, "blue": 0.7411765}}, {"type": "ACCENT2", "color": {"red": 0.05882353, "green": 0.6156863, "blue": 0.34509805}}, {"type": "ACCENT3", "color": {"red": 0.85882354, "green": 0.26666668, "blue": 0.21568628}}, {"type": "ACCENT4", "color": {"red": 0.98039216, "green": 0.98039216, "blue": 0.98039216}}, {"type": "ACCENT5", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "ACCENT6", "color": {"red": 0.95686275, "green": 0.7058824}}, {"type": "HYPERLINK", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "FOLLOWED_HYPERLINK", "color": {"red": 0.30980393, "green": 0.7647059, "blue": 0.96862745}}, {"type": "TEXT1", "color": {"red": 0.25882354, "green": 0.52156866, "blue": 0.95686275}}, {"type": "BACKGROUND1", "color": {"red": 1, "green": 1, "blue": 1}}, {"type": "TEXT2", "color": {"red": 0.4509804, "green": 0.4509804, "blue": 0.4509804}}, {"type": "BACKGROUND2", "color": {"red": 0.25882354, "green": 0.25882354, "blue": 0.25882354}}]}}, "masterProperties": {"displayName": "Material"}}]}
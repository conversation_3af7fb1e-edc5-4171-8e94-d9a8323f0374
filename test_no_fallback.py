#!/usr/bin/env python3
"""
Test script to verify fallback methods are removed
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_no_fallback():
    """Test that fallback methods are removed"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        print('✅ Service created successfully')
        
        # Test that fallback method doesn't exist
        try:
            service._create_fallback_slides_from_content
            print('❌ Fallback method still exists!')
            return False
        except AttributeError:
            print('✅ Fallback method removed successfully')
        
        # Test that service has required methods
        required_methods = [
            '_analyze_template_with_placeholders',
            '_generate_presentation_content', 
            '_map_content_to_template',
            '_filter_used_slides'
        ]
        
        for method_name in required_methods:
            if hasattr(service, method_name):
                print(f'✅ {method_name} exists')
            else:
                print(f'❌ {method_name} missing')
                return False
        
        print('🎉 All tests passed - no fallback issues!')
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    test_no_fallback()

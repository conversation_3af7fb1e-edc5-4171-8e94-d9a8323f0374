"""
Test script để kiểm tra annotation parsing
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_annotation_parsing():
    """Test annotation parsing với sample content"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample annotated content với annotation chính xác
        sample_content = """
Bài 1: Nguy<PERSON>n tố hóa học (LessonName)
Bài này giới thiệu về khái niệm nguyên tố hóa học và các tính chất c<PERSON> bản (LessonDescription)
Ng<PERSON>y thuyết trình: 12-07-2025 (CreatedDate)

I. Khái niệm nguyên tố hó<PERSON> họ<PERSON> (TitleName)
<PERSON>uyên tố hóa học là tập hợp các nguyên tử có cùng số proton trong hạt nhân. Ví dụ: Hydro H có 1 proton, Helium He có 2 proton, Carbon C có 6 proton. (TitleContent)

Số hiệu nguyên tử (SubtitleName)
Số hiệu nguyên tử Z bằng số proton trong hạt nhân nguyên tử. (SubtitleContent)

Các điểm quan trọng cần nhớ: (SubtitleContent)
• Số proton quyết định tính chất hóa học của nguyên tố (BulletItem)
• Các nguyên tử cùng nguyên tố có cùng số proton (BulletItem)
• Phản ứng hóa học không làm thay đổi số proton (BulletItem)

Hình ảnh minh họa: Bảng tuần hoàn các nguyên tố (ImageName)
Bảng tuần hoàn sắp xếp các nguyên tố theo số hiệu nguyên tử tăng dần (ImageContent)

=== SLIDE 1 SUMMARY ===
Placeholders: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, BulletItem, ImageName, ImageContent
===========================
"""
        
        logger.info("🧪 Testing annotation parsing with corrected content...")
        logger.info("📝 Sample content:")
        print(sample_content)
        print("\n" + "="*60 + "\n")
        
        # Test parsing
        parsed_result = service._parse_annotated_content(sample_content)
        
        if parsed_result:
            logger.info(f"✅ Parsing successful!")
            logger.info(f"📊 Total items: {parsed_result['total_items']}")
            logger.info(f"📊 Slides found: {len(parsed_result['slide_summaries'])}")
            
            # Show parsed data summary
            logger.info("\n📋 PARSED DATA SUMMARY:")
            for placeholder_type, items in parsed_result['parsed_data'].items():
                if items:
                    logger.info(f"  ✅ {placeholder_type}: {len(items)} items")
                    for i, item in enumerate(items):
                        content_preview = item['content'][:50] + "..." if len(item['content']) > 50 else item['content']
                        logger.info(f"    {i+1}. {content_preview}")
                else:
                    logger.info(f"  ❌ {placeholder_type}: 0 items")
            
            # Show slide summaries
            if parsed_result['slide_summaries']:
                logger.info("\n📋 SLIDE SUMMARIES:")
                for summary in parsed_result['slide_summaries']:
                    logger.info(f"  Slide {summary['slide_number']}: {summary['placeholders']}")
            
            return True
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regex_pattern():
    """Test regex pattern riêng biệt"""
    import re
    
    logger.info("🔍 Testing regex pattern...")
    
    # Valid placeholder types
    valid_placeholders = '|'.join([
        'LessonName', 'LessonDescription', 'CreatedDate', 
        'TitleName', 'TitleContent', 'SubtitleName', 'SubtitleContent', 
        'BulletItem', 'ImageName', 'ImageContent'
    ])
    annotation_pattern = rf'(.+?)\s*\(({valid_placeholders})\)'
    
    test_lines = [
        "Bài 1: Nguyên tố hóa học (LessonName)",
        "Hydro H có 1 proton (TitleContent)",
        "Nguyên tố Hydro (H)",  # Should NOT match
        "Carbon (C) có 6 proton",  # Should NOT match
        "Số hiệu nguyên tử (SubtitleName)",
        "• Điểm quan trọng (BulletItem)"
    ]
    
    for line in test_lines:
        matches = re.findall(annotation_pattern, line)
        if matches:
            for match in matches:
                content, placeholder_type = match
                logger.info(f"✅ MATCH: '{content.strip()}' -> {placeholder_type}")
        else:
            logger.info(f"❌ NO MATCH: '{line}'")
    
    return True

def main():
    """Main test function"""
    logger.info("🚀 TESTING ANNOTATION PARSING")
    logger.info("=" * 60)
    
    # Test 1: Regex pattern
    logger.info("\n🔍 TEST 1: Regex Pattern")
    regex_success = test_regex_pattern()
    
    # Test 2: Full parsing
    logger.info("\n📝 TEST 2: Full Annotation Parsing")
    parsing_success = test_annotation_parsing()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"🔍 Regex Pattern: {'PASSED' if regex_success else 'FAILED'}")
    logger.info(f"📝 Annotation Parsing: {'PASSED' if parsing_success else 'FAILED'}")
    
    if parsing_success:
        logger.info("🎉 Annotation parsing is working correctly!")
    else:
        logger.info("❌ Annotation parsing has issues - needs debugging")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()

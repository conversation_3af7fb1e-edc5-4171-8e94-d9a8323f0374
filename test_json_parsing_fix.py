#!/usr/bin/env python3
"""
Test script for JSON parsing improvements
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_json_cleaning():
    """Test the JSON cleaning function"""
    from app.services.slide_generation_service import get_slide_generation_service
    
    service = get_slide_generation_service()
    
    # Test cases with common JSON issues
    test_cases = [
        # Case 1: Trailing comma
        '''[
  {
    "slideId": "slide1",
    "elements": [
      {
        "objectId": "obj1",
        "text": "Test",
        "Type": "LessonName",
      }
    ],
  }
]''',
        
        # Case 2: Missing comma between objects
        '''[
  {
    "slideId": "slide1"
  }
  {
    "slideId": "slide2"
  }
]''',
        
        # Case 3: Markdown code blocks
        '''```json
[
  {
    "slideId": "slide1",
    "elements": []
  }
]
```''',
        
        # Case 4: Extra text before/after
        '''Here is the JSON response:

[
  {
    "slideId": "slide1",
    "elements": []
  }
]

This completes the response.'''
    ]
    
    print("🧪 Testing JSON cleaning function...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Input: {test_case[:100]}...")
        
        try:
            cleaned = service._clean_json_response(test_case)
            print(f"Cleaned: {cleaned[:100]}...")
            
            # Try to parse the cleaned JSON
            import json
            parsed = json.loads(cleaned)
            print(f"✅ Successfully parsed: {type(parsed)}")
            
        except Exception as e:
            print(f"❌ Failed: {e}")

def test_api_call():
    """Test a simple API call to see if JSON parsing works"""
    import requests
    
    print("\n🔄 Testing API call with improved JSON parsing...")
    
    payload = {
        "lesson_id": "test_lesson_001",
        "template_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "config_prompt": "Tạo slide đơn giản để test JSON parsing",
        "presentation_title": "Test JSON Parsing"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/slides/generate-slides", 
            json=payload, 
            timeout=180
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Generated {result.get('slides_created', 0)} slides")
            print(f"Presentation ID: {result.get('presentation_id')}")
        else:
            print(f"❌ Error: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 Testing JSON parsing improvements...")
    print("=" * 50)
    
    # Test JSON cleaning
    test_json_cleaning()
    
    # Test API call
    test_api_call()

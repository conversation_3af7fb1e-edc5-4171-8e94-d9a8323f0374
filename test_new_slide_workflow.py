"""
Test script cho quy trình slide generation mới
- Chỉ 1 lần gọi AI với annotation
- Xử lý bằng code thay vì AI lần 2
- Tự động xử lý max_length
"""

import asyncio
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_new_slide_workflow():
    """Test quy trình slide generation mới"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        if not service.is_available():
            logger.error("❌ Slide generation service not available")
            return False
        
        logger.info("✅ Slide generation service is available")
        
        # Test data
        lesson_id = "test_lesson_chemistry"
        template_id = "1hiKvx_qogkBh7jxfowHso9WYvA77NvFUpz3cRICzdxg"
        
        logger.info(f"🧪 Testing new workflow with lesson_id: {lesson_id}")
        logger.info(f"📋 Template ID: {template_id}")
        
        # Test the new workflow
        result = await service.generate_slides_from_lesson(
            lesson_id=lesson_id,
            template_id=template_id,
            config_prompt="Tạo slide về bài học Hóa học với phong cách sinh động, dễ hiểu cho học sinh THPT",
            presentation_title=f"Test New Workflow - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        logger.info("📊 RESULT:")
        logger.info(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            logger.info(f"✅ Presentation ID: {result.get('presentation_id')}")
            logger.info(f"✅ Slides created: {result.get('slides_created', 0)}")
            logger.info(f"✅ Web view link: {result.get('web_view_link')}")
            logger.info("🎉 NEW WORKFLOW TEST PASSED!")
            return True
        else:
            logger.error(f"❌ Error: {result.get('error')}")
            logger.error("💥 NEW WORKFLOW TEST FAILED!")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed with exception: {e}")
        return False

async def test_annotation_parsing():
    """Test annotation parsing functionality"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample annotated content
        sample_content = """
Bài 1: Cấu hình electron (LessonName)
Bài này cho chúng ta biết được cấu hình electron trong nguyên tử (LessonDescription)
Ngày thuyết trình: 12-07-2025 (CreatedDate)

I. Khái niệm cấu hình electron (TitleName)
Cấu hình electron là cách sắp xếp các electron trong các orbital của nguyên tử. (TitleContent)

Quy tắc Aufbau (SubtitleName)
Electron điền vào orbital có mức năng lượng thấp trước. (SubtitleContent)

Các điểm quan trọng: (SubtitleContent)
• Nguyên lý Pauli: Mỗi orbital chứa tối đa 2 electron (BulletItem)
• Quy tắc Hund: Electron điền đơn lẻ trước khi ghép đôi (BulletItem)

=== SLIDE 1 SUMMARY ===
Placeholders: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, BulletItem
===========================
"""
        
        logger.info("🧪 Testing annotation parsing...")
        
        # Test parsing
        parsed_result = service._parse_annotated_content(sample_content)
        
        if parsed_result:
            logger.info(f"✅ Parsing successful!")
            logger.info(f"📊 Total items: {parsed_result['total_items']}")
            logger.info(f"📊 Slides found: {len(parsed_result['slide_summaries'])}")
            
            # Show parsed data summary
            for placeholder_type, items in parsed_result['parsed_data'].items():
                if items:
                    logger.info(f"  - {placeholder_type}: {len(items)} items")
            
            return True
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Annotation parsing test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🚀 TESTING NEW SLIDE GENERATION WORKFLOW")
    logger.info("=" * 60)
    
    # Test 1: Annotation parsing
    logger.info("\n📝 TEST 1: Annotation Parsing")
    parsing_success = await test_annotation_parsing()
    
    # Test 2: Full workflow (will likely fail due to missing lesson content)
    logger.info("\n🔄 TEST 2: Full Workflow")
    workflow_success = await test_new_slide_workflow()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"✅ Annotation Parsing: {'PASSED' if parsing_success else 'FAILED'}")
    logger.info(f"🔄 Full Workflow: {'PASSED' if workflow_success else 'FAILED'}")
    
    if parsing_success:
        logger.info("🎉 Core functionality is working!")
        logger.info("💡 Full workflow may fail due to missing lesson content - this is expected")
    else:
        logger.info("❌ Core functionality has issues - needs debugging")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script cho slide generation với enhanced prompt và validation
"""

import asyncio
import json
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.slide_generation_service import SlideGenerationService
from app.services.textbook_retrieval_service import TextbookRetrievalService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_enhanced_slide_generation():
    """Test slide generation với enhanced prompt và validation"""
    print("🧪 TESTING ENHANCED SLIDE GENERATION")
    print("=" * 60)
    
    # Test data
    payload = {
        "lesson_id": "1",
        "template_id": "19QE39G1EFZGdm-_m3TY1poD5v1we2T5Z_rglalK_4mA",
        "config_prompt": """
Tạo slide gi<PERSON>o dục chuyên nghiệp với các yêu cầu:
1. <PERSON><PERSON><PERSON> dung phù hợp với học sinh trung học
2. S<PERSON> dụng bullet points để dễ đọc
3. Tiêu đề ngắn gọn, súc tích
4. Nội dung chi tiết nhưng không quá dài
5. Đảm bảo tính logic và dễ hiểu
        """.strip(),
        "presentation_title": "Bài học Toán - Hàm số bậc nhất"
    }
    
    print(f"📝 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        # Initialize services
        print("🔧 Initializing services...")
        slide_service = SlideGenerationService()
        lesson_service = TextbookRetrievalService()
        
        # Check service availability
        if not slide_service.is_available():
            print("❌ Slide generation service not available")
            return
            
        print("✅ Services initialized successfully")
        print()
        
        # Test 1: Get lesson content
        print("📚 Step 1: Getting lesson content...")
        lesson_result = await lesson_service.get_lesson_content(payload["lesson_id"])

        lesson_content = lesson_result.get("lesson_content", "")
        if not lesson_content:
            print(f"⚠️ No lesson content found for lesson_id: {payload['lesson_id']}")
            # Use fallback content for testing
            lesson_content = """
            Bài học: Hàm số bậc nhất

            1. Định nghĩa hàm số bậc nhất
            - Hàm số bậc nhất là hàm số có dạng y = ax + b (a ≠ 0)
            - Trong đó a và b là các hằng số, a gọi là hệ số góc, b gọi là tung độ gốc

            2. Tính chất của hàm số bậc nhất
            - Đồ thị là một đường thẳng
            - Hàm số đồng biến khi a > 0, nghịch biến khi a < 0
            - Đi qua điểm (0, b) và (-b/a, 0)

            3. Ứng dụng
            - Giải các bài toán thực tế
            - Mô hình hóa các hiện tượng tuyến tính
            """
        print(f"✅ Lesson content retrieved: {len(lesson_content)} characters")
        print(f"📄 Content preview: {lesson_content[:200]}...")
        print()
        
        # Test 2: Copy and analyze template
        print("🔍 Step 2: Copy and analyze template...")
        copy_result = await slide_service.slides_service.copy_and_analyze_template(
            payload["template_id"], 
            payload["presentation_title"]
        )
        
        if not copy_result["success"]:
            print(f"❌ Failed to copy template: {copy_result['error']}")
            return
            
        print(f"✅ Template copied and analyzed:")
        print(f"   - Presentation ID: {copy_result['copied_presentation_id']}")
        print(f"   - Title: {copy_result['presentation_title']}")
        print(f"   - Slides: {copy_result['slide_count']}")
        print(f"   - Web link: {copy_result['web_view_link']}")
        print()
        
        # Test 3: Analyze template structure in detail
        print("🔍 Step 3: Analyzing template structure...")
        for i, slide in enumerate(copy_result['slides']):
            print(f"   Slide {i+1} (ID: {slide['slideId']}):")
            elements = slide.get('elements', [])
            if elements:
                for j, element in enumerate(elements):
                    text_preview = element.get('text', '')[:30] + "..." if len(element.get('text', '')) > 30 else element.get('text', '')
                    actual_size = element.get('actualSize', {})
                    if actual_size:
                        width = actual_size.get('width', {}).get('magnitude', 0)
                        height = actual_size.get('height', {}).get('magnitude', 0)
                        width_pt = int(width / 12700) if width > 0 else 0
                        height_pt = int(height / 12700) if height > 0 else 0
                        print(f"     Element {j+1}: {element['objectId']}")
                        print(f"       Text: \"{text_preview}\"")
                        print(f"       Size: {width_pt}x{height_pt} points")
                        
                        # Estimate text capacity
                        chars_per_line = max(1, int(width_pt / 7))
                        lines_available = max(1, int(height_pt / 15))
                        total_chars = int(chars_per_line * lines_available * 0.8)
                        print(f"       Estimated capacity: ~{total_chars} characters")
                    else:
                        print(f"     Element {j+1}: {element['objectId']} - \"{text_preview}\"")
            else:
                print("     No editable elements")
        print()
        
        # Test 4: Generate slides content with enhanced prompt
        print("🤖 Step 4: Generating slides content with LLM...")
        slides_content_result = await slide_service._generate_slides_content(
            lesson_content,
            copy_result,
            payload.get("config_prompt")
        )
        
        if not slides_content_result["success"]:
            print(f"❌ Failed to generate slides content: {slides_content_result['error']}")
            return
            
        slides_data = slides_content_result["slides"]
        print(f"✅ Slides content generated: {len(slides_data)} slides")
        
        # Show generated content
        for i, slide in enumerate(slides_data):
            print(f"   Generated Slide {i+1}:")
            print(f"     ID: {slide.get('slideId')}")
            print(f"     Action: {slide.get('action', 'update')}")
            updates = slide.get('updates', {})
            print(f"     Updates: {len(updates)} elements")
            for element_id, content in updates.items():
                content_preview = content[:100] + "..." if len(content) > 100 else content
                print(f"       {element_id}: \"{content_preview}\"")
                print(f"       Length: {len(content)} characters")
        print()
        
        # Test 5: Update presentation with generated content
        print("📝 Step 5: Updating presentation with generated content...")
        update_result = await slide_service.slides_service.update_copied_presentation_content(
            copy_result["copied_presentation_id"],
            slides_data
        )
        
        if not update_result["success"]:
            print(f"❌ Failed to update presentation: {update_result['error']}")
            return
            
        print(f"✅ Presentation updated successfully:")
        print(f"   - Slides updated: {update_result.get('slides_updated', 0)}")
        print(f"   - Slides created: {update_result.get('slides_created', 0)}")
        print(f"   - Total requests: {update_result.get('requests_executed', 0)}")
        print()
        
        # Final result
        print("🎉 ENHANCED SLIDE GENERATION TEST COMPLETED SUCCESSFULLY!")
        print(f"📊 Final Results:")
        print(f"   - Presentation ID: {copy_result['copied_presentation_id']}")
        print(f"   - Web View Link: {copy_result['web_view_link']}")
        print(f"   - Total Slides: {copy_result['slide_count'] + update_result.get('slides_created', 0)}")
        print(f"   - Content Quality: Enhanced with size-aware formatting")
        print()
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")

if __name__ == "__main__":
    asyncio.run(test_enhanced_slide_generation())

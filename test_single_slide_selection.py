"""
Test script để kiểm tra logic chọn single slide
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_slide_selection():
    """Test slide selection logic"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Mock parsed content
        parsed_content = {
            "parsed_data": {
                "LessonName": [{"content": "Bài 1: Nguyên tố hóa học", "original_line": "..."}],
                "LessonDescription": [{"content": "Bài này giới thiệu về khái niệm nguyên tố hóa học", "original_line": "..."}],
                "CreatedDate": [{"content": "<PERSON><PERSON><PERSON> thuyết trình: 12-07-2025", "original_line": "..."}],
                "TitleName": [{"content": "I. Khái niệm nguyên tố hóa học", "original_line": "..."}],
                "TitleContent": [{"content": "Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton trong hạt nhân", "original_line": "..."}],
                "SubtitleName": [{"content": "Số hiệu nguyên tử", "original_line": "..."}],
                "SubtitleContent": [
                    {"content": "Số hiệu nguyên tử Z bằng số proton trong hạt nhân nguyên tử", "original_line": "..."},
                    {"content": "Các điểm quan trọng cần nhớ:", "original_line": "..."}
                ],
                "BulletItem": [
                    {"content": "• Số proton quyết định tính chất hóa học của nguyên tố", "original_line": "..."},
                    {"content": "• Các nguyên tử cùng nguyên tố có cùng số proton", "original_line": "..."},
                    {"content": "• Phản ứng hóa học không làm thay đổi số proton", "original_line": "..."}
                ],
                "ImageName": [{"content": "Hình ảnh minh họa: Bảng tuần hoàn các nguyên tố", "original_line": "..."}],
                "ImageContent": [{"content": "Bảng tuần hoàn sắp xếp các nguyên tố theo số hiệu nguyên tử tăng dần", "original_line": "..."}]
            },
            "slide_summaries": [
                {
                    "slide_number": 1,
                    "placeholders": ["LessonName", "LessonDescription", "CreatedDate", "TitleName", "TitleContent", "SubtitleName", "SubtitleContent", "BulletItem", "ImageName", "ImageContent"],
                    "content": []
                }
            ],
            "total_items": 13
        }
        
        # Mock analyzed template với nhiều slides
        analyzed_template = {
            "slides": [
                {
                    "slideId": "slide_1",
                    "description": "Slide dành cho 1 LessonName, 1 LessonDescription, 1 CreatedDate",
                    "elements": [
                        {"objectId": "obj_1", "Type": "LessonName", "max_length": 50},
                        {"objectId": "obj_2", "Type": "LessonDescription", "max_length": 100},
                        {"objectId": "obj_3", "Type": "CreatedDate", "max_length": 30}
                    ],
                    "placeholder_counts": {"LessonName": 1, "LessonDescription": 1, "CreatedDate": 1}
                },
                {
                    "slideId": "slide_2", 
                    "description": "Slide dành cho 1 TitleName, 1 TitleContent",
                    "elements": [
                        {"objectId": "obj_4", "Type": "TitleName", "max_length": 40},
                        {"objectId": "obj_5", "Type": "TitleContent", "max_length": 200}
                    ],
                    "placeholder_counts": {"TitleName": 1, "TitleContent": 1}
                },
                {
                    "slideId": "slide_3",
                    "description": "Slide dành cho 1 SubtitleName, 2 SubtitleContent, 3 BulletItem",
                    "elements": [
                        {"objectId": "obj_6", "Type": "SubtitleName", "max_length": 40},
                        {"objectId": "obj_7", "Type": "SubtitleContent", "max_length": 150},
                        {"objectId": "obj_8", "Type": "SubtitleContent", "max_length": 150},
                        {"objectId": "obj_9", "Type": "BulletItem", "max_length": 80},
                        {"objectId": "obj_10", "Type": "BulletItem", "max_length": 80},
                        {"objectId": "obj_11", "Type": "BulletItem", "max_length": 80}
                    ],
                    "placeholder_counts": {"SubtitleName": 1, "SubtitleContent": 2, "BulletItem": 3}
                },
                {
                    "slideId": "slide_4",
                    "description": "Slide dành cho 1 ImageName, 1 ImageContent",
                    "elements": [
                        {"objectId": "obj_12", "Type": "ImageName", "max_length": 60},
                        {"objectId": "obj_13", "Type": "ImageContent", "max_length": 120}
                    ],
                    "placeholder_counts": {"ImageName": 1, "ImageContent": 1}
                },
                {
                    "slideId": "slide_all",
                    "description": "Slide dành cho tất cả placeholder types",
                    "elements": [
                        {"objectId": "obj_all_1", "Type": "LessonName", "max_length": 50},
                        {"objectId": "obj_all_2", "Type": "LessonDescription", "max_length": 100},
                        {"objectId": "obj_all_3", "Type": "CreatedDate", "max_length": 30},
                        {"objectId": "obj_all_4", "Type": "TitleName", "max_length": 40},
                        {"objectId": "obj_all_5", "Type": "TitleContent", "max_length": 200},
                        {"objectId": "obj_all_6", "Type": "SubtitleName", "max_length": 40},
                        {"objectId": "obj_all_7", "Type": "SubtitleContent", "max_length": 150},
                        {"objectId": "obj_all_8", "Type": "BulletItem", "max_length": 80},
                        {"objectId": "obj_all_9", "Type": "ImageName", "max_length": 60},
                        {"objectId": "obj_all_10", "Type": "ImageContent", "max_length": 120}
                    ],
                    "placeholder_counts": {"LessonName": 1, "LessonDescription": 1, "CreatedDate": 1, "TitleName": 1, "TitleContent": 1, "SubtitleName": 1, "SubtitleContent": 1, "BulletItem": 1, "ImageName": 1, "ImageContent": 1}
                }
            ],
            "total_slides": 5,
            "original_info": {}
        }
        
        logger.info("🧪 Testing slide selection logic...")
        logger.info(f"📊 Available slides: {len(analyzed_template['slides'])}")
        for slide in analyzed_template['slides']:
            logger.info(f"  - {slide['slideId']}: {slide['description']}")
        
        # Test mapping
        result = await service._map_parsed_content_to_slides(parsed_content, analyzed_template)
        
        if result["success"]:
            mapped_slides = result["slides"]
            logger.info(f"✅ Mapping successful!")
            logger.info(f"📊 Slides selected: {len(mapped_slides)}")
            
            for slide in mapped_slides:
                slide_id = slide.get("slideId")
                elements = slide.get("elements", [])
                logger.info(f"  ✅ Selected slide: {slide_id}")
                logger.info(f"    📋 Elements mapped: {len(elements)}")
                for element in elements:
                    content_preview = element.get("text", "")[:50] + "..." if len(element.get("text", "")) > 50 else element.get("text", "")
                    logger.info(f"      - {element.get('Type')}: {content_preview}")
            
            # Kiểm tra xem có chỉ chọn 1 slide không
            if len(mapped_slides) == 1:
                logger.info("🎉 PERFECT! Chỉ chọn 1 slide như yêu cầu!")
                selected_slide = mapped_slides[0]
                expected_slide = "slide_all"  # Should select the slide that can handle all content
                if selected_slide["slideId"] == expected_slide:
                    logger.info(f"✅ Chọn đúng slide tốt nhất: {expected_slide}")
                else:
                    logger.info(f"⚠️ Chọn slide: {selected_slide['slideId']}, expected: {expected_slide}")
                return True
            else:
                logger.error(f"❌ Chọn {len(mapped_slides)} slides, expected: 1")
                return False
        else:
            logger.error(f"❌ Mapping failed: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🚀 TESTING SINGLE SLIDE SELECTION")
    logger.info("=" * 60)
    
    success = await test_slide_selection()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"🎯 Single Slide Selection: {'PASSED' if success else 'FAILED'}")
    
    if success:
        logger.info("🎉 Single slide selection is working correctly!")
        logger.info("✅ System will select only the best matching slide")
        logger.info("✅ Unused slides will be automatically deleted")
    else:
        logger.info("❌ Single slide selection has issues - needs debugging")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

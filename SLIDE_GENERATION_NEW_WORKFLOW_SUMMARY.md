# Slide Generation API - New Workflow Implementation

## 📋 Tóm tắt thay đổi

Đã sửa lại API `/api/v1/slides/generate-slides` theo yêu cầu mới với quy trình 2 lần gọi AI và hỗ trợ placeholder enum.

## 🔧 Thay đổi chính

### 1. Enum Placeholder Support

Đã implement hỗ trợ các placeholder enum theo yêu cầu:

- `LessonName`: Tên bài học
- `LessonDescription`: Tóm tắt bài học
- `CreatedDate`: Ngày tạo
- `TitleName`: Tên mục lớn
- `TitleContent`: Nội dung mục lớn
- `SubtitleName`: Tên mục nhỏ
- `SubtitleContent`: Nội dung mục nhỏ
- `BulletItem`: Ý nằm trong mục nhỏ
- `ImageName`: Tên Image (text only)
- `ImageContent`: Nội dung Image (text only)

### 2. Template Analysis Enhancement

- Thêm field `Type` cho từng element dựa vào text format `"PlaceholderName <max_length>"`
- Thêm field `description` cho mỗi slide (auto-generate theo format)
- Truyền xuống font-style và font-size information
- Bỏ qua thông tin position/bounding-box trong output

### 3. Two-Phase AI Workflow (UPDATED)

**Phase 1: Generate Presentation Content**

- Input: `lesson_content`
- Output: `presentation-content` (TEXT thuần túy, không phải JSON)
- Purpose: Tạo nội dung thuyết trình đầy đủ dưới dạng markdown text
- Format: Markdown với headers (#, ##, ###), bullet points (•), và cấu trúc rõ ràng

**Phase 2: Map Content to Template**

- Input: `presentation-content` (text) + `analyzed template`
- Output: Mapped slides với content đã fill vào placeholders (JSON format)
- Purpose: Ghép nội dung text vào template theo đúng placeholder types

### 4. Smart Slide Selection Logic

- Chỉ sử dụng slides có đủ placeholder types phù hợp với content
- Ví dụ: Content có 1 TitleName + 2 SubtitleName → chọn slide có đủ các placeholder này
- Tự động xóa slides không sử dụng khỏi output final

### 5. Content Length Compliance

- Tuân thủ nghiêm ngặt `max_length` của từng placeholder
- AI tự động viết lại content nếu vượt quá giới hạn
- Đảm bảo không vỡ layout

## 🏗️ Cấu trúc code mới

### Core Methods

```python
# Main workflow
async def _generate_slides_content()

# Phase 1: Content generation
async def _generate_presentation_content()
def _create_presentation_content_prompt()

# Phase 2: Content mapping
async def _map_content_to_template()
def _create_content_mapping_prompt()

# Template analysis
def _analyze_template_with_placeholders()
def _detect_placeholder_type_from_text()
def _map_to_placeholder_enum()
def _detect_placeholder_by_content()

# Utilities
def _generate_slide_description()
def _extract_font_size()
def _extract_font_family()
def _extract_font_style()
def _filter_used_slides()
```

## 📊 API Response Format

### Input (unchanged)

```json
{
  "lesson_id": "lesson_123",
  "template_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "config_prompt": "Optional custom prompt",
  "presentation_title": "Optional title"
}
```

### Output (enhanced)

```json
{
  "success": true,
  "lesson_id": "lesson_123",
  "presentation_id": "new_presentation_id",
  "presentation_title": "Generated Title",
  "web_view_link": "https://docs.google.com/presentation/d/...",
  "slides_created": 5,
  "template_info": {
    "title": "Template Title",
    "layouts_count": 3
  },
  "presentation_content": {...},  // Debug info
  "analyzed_template": {...}      // Debug info
}
```

## 🔄 Workflow Steps (UPDATED)

1. **Template Analysis**: Phân tích template và detect placeholder types
2. **Content Generation**: AI sinh presentation-content từ lesson_content (TEXT format)
3. **Content Mapping**: AI map text content vào template placeholders (JSON output)
4. **Slide Filtering**: Chỉ giữ slides được sử dụng, xóa slides thừa
5. **Google Slides Update**: Cập nhật content vào Google Slides

### Chi tiết Phase 1 - Content Generation:

```
Input: lesson_content (text)
↓
AI Processing với prompt tạo text thuyết trình
↓
Output: Markdown text với cấu trúc:
# Tên bài học
## Tóm tắt
## I. Phần chính 1
### 1.1 Mục nhỏ
• Bullet point 1
• Bullet point 2
```

### Chi tiết Phase 2 - Content Mapping:

```
Input:
- presentation_content (markdown text)
- analyzed_template (với placeholder types)
↓
AI Processing với prompt mapping
↓
Output: JSON array với slides đã map content
[{
  "slideId": "slide1",
  "elements": [{
    "objectId": "obj1",
    "text": "Mapped content",
    "Type": "LessonName",
    "max_length": 50
  }]
}]
```

## ✅ Compliance với yêu cầu

- ✅ Enum placeholder support (10 types)
- ✅ Template analysis với Type và max_length
- ✅ Slide description auto-generation
- ✅ Font-style và font-size information
- ✅ Position information excluded from output
- ✅ Two-phase AI workflow
- ✅ Smart slide selection logic
- ✅ Content length compliance
- ✅ Unused slide removal
- ✅ Metadata preservation (slideId, objectId, font info)

## 🔧 Technical Improvements

### JSON Parsing Enhancement

- **Multiple parsing methods**: Object extraction, full response, cleanup
- **Error handling**: Robust fallback mechanisms
- **JSON cleaning**: Remove markdown blocks, fix trailing commas, normalize format
- **Detailed logging**: Debug information for troubleshooting

### Text-based Workflow Benefits

- **Simpler Phase 1**: No JSON parsing needed, direct text output
- **Better AI understanding**: Text format is more natural for AI
- **Reduced errors**: Eliminates JSON parsing issues in first phase
- **Improved reliability**: More robust content generation

## 🧪 Testing

Sử dụng script test:

```bash
python test_new_slide_service.py  # Test service functionality
python test_new_slide_api.py      # Test API endpoint
python test_text_workflow.py      # Test new text-based workflow
```

## 📝 Notes

- API endpoint không thay đổi: `/api/v1/slides/generate-slides`
- Backward compatible với existing requests
- Enhanced với new workflow và placeholder enum support
- Improved error handling và logging
- Modular code structure for easy maintenance

"""
Test script để kiểm tra hiểu biết về cấu trúc phân cấp
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_prompt_hierarchy_guidance():
    """Test prompt có hướng dẫn về cấu trúc phân cấp"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        lesson_content = "Sample lesson content"
        prompt = service._create_annotated_presentation_prompt(lesson_content)
        
        # Check for hierarchy guidance
        hierarchy_indicators = [
            "CẤU TRÚC PHÂN CẤP",
            "TitleName: CHỈ là tiêu đề mục lớn",
            "TitleContent: Nội dung giải thích của mục lớn",
            "SubtitleName: CHỈ là tiêu đề mục nhỏ",
            "SubtitleContent: Nội dung giải thích của mục nhỏ",
            "VÍ DỤ CẤU TRÚC ĐÚNG",
            "VÍ DỤ SAI THƯỜNG GẶP"
        ]
        
        found_indicators = []
        for indicator in hierarchy_indicators:
            if indicator in prompt:
                found_indicators.append(indicator)
        
        logger.info(f"📝 Hierarchy indicators found: {found_indicators}")
        
        if len(found_indicators) >= 5:
            logger.info("✅ Prompt correctly includes hierarchy guidance")
            return True
        else:
            logger.warning(f"⚠️ Missing hierarchy guidance in prompt")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        return False

def test_parsing_correct_hierarchy():
    """Test parsing với cấu trúc phân cấp đúng"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample content với cấu trúc phân cấp ĐÚNG
        correct_content = """
Bài 1: Nguyên tố hóa học (LessonName)
Bài này giới thiệu về khái niệm nguyên tố hóa học (LessonDescription)
Ngày thuyết trình: 12-07-2025 (CreatedDate)

=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================

I. Khái niệm nguyên tố (TitleName)
Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton trong hạt nhân. (TitleContent)

=== SLIDE 2 SUMMARY ===
Placeholders: 1xTitleName, 1xTitleContent
===========================

II. Số hiệu nguyên tử và số khối (TitleName)

1. Số hiệu nguyên tử (SubtitleName)
Số hiệu nguyên tử (Z) là số proton trong hạt nhân của một nguyên tử. (SubtitleContent)

2. Số khối (SubtitleName)
Số khối (A) là tổng số proton và neutron trong hạt nhân. A = Z + N (SubtitleContent)

3. Ký hiệu nguyên tử (SubtitleName)
Ký hiệu nguyên tử: ᴬZX, trong đó X là ký hiệu hóa học của nguyên tố. (SubtitleContent)

=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 3xSubtitleName, 3xSubtitleContent
===========================
"""
        
        logger.info("🧪 Testing parsing with CORRECT hierarchy...")
        
        parsed_result = service._parse_annotated_content(correct_content)
        
        if parsed_result:
            parsed_data = parsed_result.get("parsed_data", {})
            slide_summaries = parsed_result.get("slide_summaries", [])
            
            logger.info(f"📊 Parsed data summary:")
            for ptype, items in parsed_data.items():
                if items:
                    logger.info(f"  {ptype}: {len(items)} items")
                    for i, item in enumerate(items):
                        content_preview = item['content'][:50] + "..." if len(item['content']) > 50 else item['content']
                        logger.info(f"    {i+1}. {content_preview}")
            
            # Check slide 3 structure
            if len(slide_summaries) >= 3:
                slide_3 = slide_summaries[2]  # Index 2 for slide 3
                counts_3 = slide_3.get("placeholder_counts", {})
                
                expected_counts = {
                    "TitleName": 1,
                    "SubtitleName": 3,
                    "SubtitleContent": 3
                }
                
                logger.info(f"📊 Slide 3 expected counts: {expected_counts}")
                logger.info(f"📊 Slide 3 actual counts: {counts_3}")
                
                # Check if counts match
                counts_match = all(counts_3.get(key) == value for key, value in expected_counts.items())
                
                if counts_match:
                    logger.info("✅ Slide 3 structure correctly parsed")
                    return True
                else:
                    logger.warning("⚠️ Slide 3 structure mismatch")
                    return False
            else:
                logger.warning(f"⚠️ Expected 3+ slides, got {len(slide_summaries)}")
                return False
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parsing_incorrect_hierarchy():
    """Test parsing với cấu trúc phân cấp SAI (như ví dụ của user)"""
    try:
        from app.services.slide_generation_service import get_slide_generation_service
        
        service = get_slide_generation_service()
        
        # Sample content với cấu trúc phân cấp SAI (như user đã chỉ ra)
        incorrect_content = """
II. Số hiệu nguyên tử, số khối, kí hiệu nguyên tử (TitleName)
Số hiệu nguyên tử (Z) là số proton trong hạt nhân của một nguyên tử. (SubtitleName)
Số khối (A) là tổng số proton và neutron trong hạt nhân của một nguyên tử. A = Z + N (SubtitleName)
Kí hiệu nguyên tử: ᴬX, trong đó X là kí hiệu hóa học của nguyên tố, Z là số hiệu nguyên tử và A là số khối. (SubtitleName)
Ví dụ: ⁴₂He (SubtitleContent)

=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 3xSubtitleName, 1xSubtitleContent
===========================
"""
        
        logger.info("🧪 Testing parsing with INCORRECT hierarchy (user's example)...")
        
        parsed_result = service._parse_annotated_content(incorrect_content)
        
        if parsed_result:
            parsed_data = parsed_result.get("parsed_data", {})
            
            # Analyze what was parsed
            subtitle_names = parsed_data.get("SubtitleName", [])
            subtitle_contents = parsed_data.get("SubtitleContent", [])
            
            logger.info(f"📊 SubtitleName items: {len(subtitle_names)}")
            for i, item in enumerate(subtitle_names):
                logger.info(f"  {i+1}. {item['content']}")
            
            logger.info(f"📊 SubtitleContent items: {len(subtitle_contents)}")
            for i, item in enumerate(subtitle_contents):
                logger.info(f"  {i+1}. {item['content']}")
            
            # This should show the problem: content being labeled as SubtitleName
            problem_detected = False
            for item in subtitle_names:
                content = item['content']
                # Check if this looks like content rather than a title
                if len(content) > 50 or "là" in content or "trong" in content:
                    logger.warning(f"⚠️ PROBLEM DETECTED: This looks like content but labeled as SubtitleName: {content[:50]}...")
                    problem_detected = True
            
            if problem_detected:
                logger.info("✅ Successfully detected the hierarchy problem")
                return True
            else:
                logger.info("⚠️ Problem not clearly detected")
                return False
        else:
            logger.error("❌ Parsing failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("🚀 TESTING HIERARCHICAL STRUCTURE UNDERSTANDING")
    logger.info("=" * 60)
    
    # Test 1: Prompt hierarchy guidance
    logger.info("\n📝 TEST 1: Prompt Hierarchy Guidance")
    guidance_success = test_prompt_hierarchy_guidance()
    
    # Test 2: Correct hierarchy parsing
    logger.info("\n✅ TEST 2: Correct Hierarchy Parsing")
    correct_success = test_parsing_correct_hierarchy()
    
    # Test 3: Detect incorrect hierarchy
    logger.info("\n❌ TEST 3: Detect Incorrect Hierarchy (User's Example)")
    incorrect_success = test_parsing_incorrect_hierarchy()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"📝 Prompt Hierarchy Guidance: {'PASSED' if guidance_success else 'FAILED'}")
    logger.info(f"✅ Correct Hierarchy Parsing: {'PASSED' if correct_success else 'FAILED'}")
    logger.info(f"❌ Detect Incorrect Hierarchy: {'PASSED' if incorrect_success else 'FAILED'}")
    
    if guidance_success and correct_success:
        logger.info("🎉 Hierarchical structure improvements working!")
        logger.info("✅ Prompt provides clear hierarchy guidance")
        logger.info("✅ System can parse correct hierarchy structure")
        logger.info("💡 AI should now understand the difference between:")
        logger.info("   - TitleName (mục lớn) vs TitleContent (nội dung mục lớn)")
        logger.info("   - SubtitleName (mục nhỏ) vs SubtitleContent (nội dung mục nhỏ)")
    else:
        logger.info("❌ Hierarchical structure understanding has issues")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()

"""
Test script để verify slide reuse và duplicate functionality
"""

import asyncio
import logging
from app.services.slide_generation_service import get_slide_generation_service

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_slide_reuse_and_duplicate():
    """Test slide generation với reuse và duplicate functionality"""

    print("🧪 Testing Slide Reuse and Duplicate Functionality")
    print("=" * 60)

    # Test data
    lesson_id = "test_lesson_001"
    template_id = "1Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with actual template ID

    try:
        # Initialize service
        slide_service = get_slide_generation_service()

        if not slide_service.is_available():
            print("❌ Slide generation service not available")
            return

        print("✅ Slide generation service initialized")

        # Test slide generation
        print("\n📝 Testing slide generation with reuse and duplicate...")
        result = await slide_service.generate_slides_from_lesson(
            lesson_id=lesson_id,
            template_id=template_id,
            presentation_title="Test Slide Reuse and Duplicate"
        )

        if result["success"]:
            print("✅ Slide generation successful!")
            print(f"   Presentation ID: {result['presentation_id']}")
            print(f"   Slides created: {result['slides_created']}")
            print(f"   Web view link: {result['web_view_link']}")

            # Verify unique slide IDs
            print("\n🔍 Verifying slide uniqueness...")
            # This would require additional API call to check actual slides

        else:
            print(f"❌ Slide generation failed: {result['error']}")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test error")

def test_slide_id_generation():
    """Test slide ID generation logic"""
    print("\n🧪 Testing Slide ID Generation Logic")
    print("-" * 40)

    # Test unique slide ID generation
    slide_ids = []
    for i in range(1, 6):
        slide_id = f"content_slide_{i}"
        slide_ids.append(slide_id)
        print(f"Generated slide ID {i}: {slide_id}")

    # Check uniqueness
    unique_ids = set(slide_ids)
    if len(unique_ids) == len(slide_ids):
        print("✅ All slide IDs are unique")
    else:
        print("❌ Duplicate slide IDs found!")
        print(f"   Total: {len(slide_ids)}, Unique: {len(unique_ids)}")

    return len(unique_ids) == len(slide_ids)

if __name__ == "__main__":
    print("🚀 Starting Slide Reuse and Duplicate Tests")

    # Test 1: Slide ID generation
    id_test_passed = test_slide_id_generation()

    # Test 2: Full slide generation (requires actual API)
    # asyncio.run(test_slide_reuse_and_duplicate())

    print(f"\n📊 Test Results:")
    print(f"   Slide ID Generation: {'✅ PASSED' if id_test_passed else '❌ FAILED'}")
    print(f"   Full Generation Test: ⏭️ SKIPPED (requires API)")

    print("\n✅ Tests completed!")
"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (QUY TRÌNH MỚI)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting NEW slide generation process for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc của bản sao (QUY TRÌNH MỚI)
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM với cấu trúc của bản sao
            slides_content = await self._generate_slides_content(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content
            logger.info(f"------------------Generated slides content: {slides_content}")

            # Bước 4: Cập nhật nội dung vào bản sao đã tạo
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            # Bước 5: Cleanup - Xóa TẤT CẢ slide template gốc, chỉ giữ slides có nội dung lesson
            slides_with_content = []
            original_template_slides = []

            # Collect slides with lesson content và track original template slides
            for slide_data in slides_content["slides"]:
                slide_id = slide_data.get('slideId')
                if slide_id:
                    if slide_id.startswith('new_slide_'):
                        # Đây là slide mới được tạo - giữ lại
                        slides_with_content.append(slide_id)
                    else:
                        # Đây là slide template được reuse - giữ lại
                        slides_with_content.append(slide_id)

            # Get all original template slide IDs để xóa những slide không được sử dụng
            for slide in copy_and_analyze_result.get("slides", []):
                original_slide_id = slide.get("slideId")
                if original_slide_id and original_slide_id not in slides_with_content:
                    original_template_slides.append(original_slide_id)

            logger.info(f"📊 Cleanup summary:")
            logger.info(f"   Slides with lesson content: {len(slides_with_content)} - {slides_with_content}")
            logger.info(f"   Original template slides to delete: {len(original_template_slides)} - {original_template_slides}")

            # Xóa các slide template gốc không được sử dụng
            if original_template_slides:
                delete_result = await self.slides_service.delete_unused_slides(
                    copy_and_analyze_result["copied_presentation_id"],
                    slides_with_content  # Keep slides with content, delete the rest
                )
                logger.info(f"Template cleanup result: {delete_result}")
            else:
                logger.info("✅ All template slides are being used - no cleanup needed")

            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": update_result.get("slides_updated", 0) + update_result.get("slides_created", 0),
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "layouts_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM theo quy trình MỚI (chỉ 1 lần gọi AI + xử lý code)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy và phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Bước 1: Phân tích template và thêm placeholder types
            logger.info("🔍 Step 1: Analyzing template and detecting placeholder types...")
            analyzed_template = self._analyze_template_with_placeholders(copied_presentation_info)

            # Bước 2: Lần 1 gọi AI - Sinh presentation-content với annotation
            logger.info("🤖 Step 2: Single AI call - Generate annotated presentation content...")
            presentation_content = await self._generate_annotated_presentation_content(
                lesson_content,
                config_prompt
            )
            if not presentation_content["success"]:
                return presentation_content
            logger.info(f"-----------------------Generated presentation content: {presentation_content}")

            # Bước 3: Xử lý bằng code - Parse và map content vào template
            logger.info("🔧 Step 3: Code-based processing - Parse and map content to template...")
            mapped_slides = await self._parse_and_map_content_to_template(
                presentation_content["content"],
                analyzed_template
            )
            if not mapped_slides["success"]:
                return mapped_slides

            # Bước 4: Lọc và chỉ giữ slides được sử dụng
            logger.info("🧹 Step 4: Filter and keep only used slides...")
            final_slides = self._filter_used_slides(mapped_slides["slides"])

            return {
                "success": True,
                "slides": final_slides,
                "presentation_content": presentation_content["content"],  # For debugging
                "analyzed_template": analyzed_template  # For debugging
            }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_template_with_placeholders(self, copied_presentation_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phân tích template và thêm placeholder types theo enum yêu cầu

        Args:
            copied_presentation_info: Thông tin presentation đã copy

        Returns:
            Dict chứa template đã phân tích với placeholder types
        """
        try:
            analyzed_slides = []

            for slide in copied_presentation_info.get("slides", []):
                analyzed_elements = []
                placeholder_counts = {}

                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    if text:  # Chỉ xử lý elements có text
                        logger.info(f"🔍 Processing text in slide {slide.get('slideId')}: '{text}'")

                        # Detect placeholder type và max_length từ text
                        placeholder_result = self._detect_placeholder_type_from_text(text)

                        if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                            placeholder_type, max_length = placeholder_result

                            logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                            # Đếm số lượng placeholder types
                            placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                            # Tạo analyzed element với thông tin đầy đủ
                            analyzed_element = {
                                "objectId": element.get("objectId"),
                                "text": None,  # LLM sẽ insert nội dung sau
                                "Type": placeholder_type,
                                "max_length": max_length,
                            }

                            analyzed_elements.append(analyzed_element)
                        else:
                            # Bỏ qua text không phải placeholder format
                            logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                            continue

                # Tạo description cho slide dựa trên placeholder counts
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("slideId"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts  # For logic selection
                }

                analyzed_slides.append(analyzed_slide)

            return {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "original_info": copied_presentation_info
            }

        except Exception as e:
            logger.error(f"Error analyzing template with placeholders: {e}")
            return {"slides": [], "total_slides": 0, "original_info": copied_presentation_info}

    def _detect_placeholder_type_from_text(self, text: str) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName <max_length>"

        Args:
            text: Text từ element

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            # Tìm pattern "PlaceholderName max_length" (không có dấu < >)
            pattern = r'(\w+)\s+(\d+)'
            match = re.search(pattern, text)

            if match:
                placeholder_name = match.group(1)
                max_length = int(match.group(2))

                # Map placeholder name to enum
                placeholder_type = self._map_to_placeholder_enum(placeholder_name)
                if placeholder_type:  # Chỉ return nếu tìm thấy valid placeholder
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _map_to_placeholder_enum(self, placeholder_name: str) -> Optional[str]:
        """
        Map placeholder name to enum values

        Args:
            placeholder_name: Name from text

        Returns:
            str: Enum placeholder type
        """
        # Mapping dictionary
        mapping = {
            "LessonName": "LessonName",
            "LessonDescription": "LessonDescription",
            "CreatedDate": "CreatedDate",
            "TitleName": "TitleName",
            "TitleContent": "TitleContent",
            "SubtitleName": "SubtitleName",
            "SubtitleContent": "SubtitleContent",
            "BulletItem": "BulletItem",
            "ImageName": "ImageName",
            "ImageContent": "ImageContent"
        }

        return mapping.get(placeholder_name)  # Return None if not found


    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _generate_annotated_presentation_content(
        self,
        lesson_content: str,
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Lần 1 gọi AI: Sinh presentation-content với annotation rõ ràng

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa presentation content đã sinh với annotation (text thuần túy)
        """
        try:
            # Tạo prompt cho lần gọi AI với annotation requirements
            prompt = self._create_annotated_presentation_prompt(lesson_content, config_prompt)

            logger.info(f"AI call prompt length: {len(prompt)} characters")

            # Gọi LLM với retry logic
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"AI call attempt {attempt + 1}/{max_retries}")

                # Tăng max_tokens cho slide generation vì response có thể dài
                llm_result = await self.llm_service.generate_content(
                    prompt=prompt,
                    temperature=0.1,
                    max_tokens=50000
                )

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    logger.info(f"AI call successful on attempt {attempt + 1}")

                    # Return the annotated text content
                    presentation_content = llm_result["text"].strip()
                    logger.debug(f"AI response length: {len(presentation_content)} characters")
                    logger.debug(f"AI response preview: {presentation_content[:200]}...")

                    return {
                        "success": True,
                        "content": presentation_content
                    }
                else:
                    logger.warning(f"AI call attempt {attempt + 1} failed: {llm_result.get('error', 'Empty response')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"AI call failed after {max_retries} attempts: {llm_result.get('error', 'Empty response')}"
                        }

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            return {
                "success": False,
                "error": "AI call failed"
            }

        except Exception as e:
            logger.error(f"Error in AI call: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_annotated_presentation_prompt(
        self,
        lesson_content: str,
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho lần gọi AI với annotation requirements

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            str: Prompt cho AI với annotation requirements
        """
        default_config = """
Bạn là chuyên gia thiết kế nội dung thuyết trình giáo dục. Nhiệm vụ của bạn là phân tích nội dung bài học và tạo ra nội dung thuyết trình.

NGUYÊN TẮC THIẾT KẾ:
1. PHÂN TÍCH TOÀN DIỆN - Hiểu rõ nội dung bài học và chia thành các phần logic
2. CẤU TRÚC RÕ RÀNG - Từ tổng quan đến chi tiết, có thứ tự logic
3. NỘI DUNG PHONG PHÚ VÀ CHI TIẾT - Tạo ít nhất 5-7 slides với nội dung đầy đủ
4. ANNOTATION CHÍNH XÁC - Mỗi câu/tiêu đề phải có annotation placeholder rõ ràng
5. KÝ HIỆU KHOA HỌC CHÍNH XÁC - Sử dụng Unicode cho công thức
6. SLIDE SUMMARIES CHI TIẾT - Ghi rõ số lượng từng placeholder type

YÊU CẦU ANNOTATION:
- Mỗi câu hoặc tiêu đề PHẢI có annotation bằng ngoặc đơn () chỉ rõ placeholder type
- Placeholder types hỗ trợ: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, ImageName, ImageContent
- TẠMTHỜI KHÔNG SỬ DỤNG BulletItem - chỉ dùng 9 placeholder types trên
- CHỈ SỬ DỤNG CÁC PLACEHOLDER TYPES TRÊN - KHÔNG dùng ký hiệu hóa học như (H), (O), (Si)
- Annotation phải chính xác và nhất quán
- CẦN có slide summaries với SỐ LƯỢNG RÕ RÀNG để hỗ trợ chọn slide template phù hợp
"""

        # final_config = config_prompt if config_prompt else default_config
        final_config = default_config
        prompt = f"""
{final_config}

NỘI DUNG BÀI HỌC:
{lesson_content}

HƯỚNG DẪN TẠO PRESENTATION CONTENT VỚI ANNOTATION:

1. PHÂN TÍCH BÀI HỌC:
   - Xác định chủ đề chính và các chủ đề phụ
   - Chia nội dung thành các phần logic (slides)
   - Mỗi phần có nội dung đầy đủ, chi tiết
   - Xác định thông tin quan trọng cần nhấn mạnh
   - Tránh lược bỏ các thông tin quan trọng trong nội dung bài học được cung cấp
2. TẠO NỘI DUNG VỚI ANNOTATION:
   - Mỗi câu/tiêu đề PHẢI có annotation (PlaceholderType) ngay sau
   - Ví dụ: "Bài 1: Cấu hình phân tử (LessonName)"
   - Ví dụ: "Bài này cho chúng ta biết được cấu hình... (LessonDescription)"
   - Ví dụ: "Ngày thuyết trình: 12-07-2025 (CreatedDate)"
   - TẠMTHỜI KHÔNG dùng BulletItem - chỉ dùng 9 placeholder types còn lại

3. HIỂU RÕ CẤU TRÚC PHÂN CẤP:
   - TitleName: Tên mục lớn (I., II., III.) - CHỈ LÀ TIÊU ĐỀ
   - TitleContent: Nội dung giải thích thuộc mục lớn đó - NỘI DUNG CHI TIẾT
   - SubtitleName: Tên mục nhỏ bên trong mục lớn (1., 2., 3.) - CHỈ LÀ TIÊU ĐỀ CON
   - SubtitleContent: Nội dung giải thích thuộc mục nhỏ đó - NỘI DUNG CHI TIẾT CON

4. FORMAT CHI TIẾT VỚI CẤU TRÚC PHÂN CẤP RÕ RÀNG:

SLIDE 1 - GIỚI THIỆU:
Bài [Số]: [Tên bài học] (LessonName)
[Tóm tắt ngắn gọn về bài học] (LessonDescription)
Ngày thuyết trình: 12-07-2025 (CreatedDate)

=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================

SLIDE 2 - MỤC LỚN VỚI NỘI DUNG TỔNG QUÁT:
I. [Tên mục lớn] (TitleName)
[Nội dung tổng quát giải thích về mục lớn này, khái niệm chung, định nghĩa] (TitleContent)

=== SLIDE 2 SUMMARY ===
Placeholders: 1xTitleName, 1xTitleContent
===========================

SLIDE 3 - CHI TIẾT CÁC MỤC NHỎ TRONG MỤC LỚN:
II. [Tên mục lớn khác] (TitleName)

1. [Tên mục nhỏ thứ nhất] (SubtitleName)
[Nội dung chi tiết của mục nhỏ thứ nhất] (SubtitleContent)

2. [Tên mục nhỏ thứ hai] (SubtitleName)
[Nội dung chi tiết của mục nhỏ thứ hai] (SubtitleContent)

=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 2xSubtitleName, 2xSubtitleContent
===========================

SLIDE 4 - HÌNH ẢNH MINH HỌA:
Hình ảnh mô tả: [Tên hình ảnh] (ImageName)
[Mô tả chi tiết nội dung hình ảnh] (ImageContent)

=== SLIDE 4 SUMMARY ===
Placeholders: 1xImageName, 1xImageContent
===========================

... (tiếp tục với các slide khác tùy theo nội dung bài học)

4. QUY TẮC ANNOTATION:
   - LUÔN có annotation (PlaceholderType) sau mỗi câu/tiêu đề
   - Sử dụng đúng placeholder types: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, BulletItem, ImageName, ImageContent
   - TUYỆT ĐỐI KHÔNG dùng ký hiệu hóa học như (H), (O), (Si), (He), (C) làm annotation
   - Annotation phải nhất quán và chính xác
   - Nội dung phải phù hợp với placeholder type

   VÍ DỤ SAI: "Nguyên tố Hydro (H)" - KHÔNG làm thế này!
   VÍ DỤ ĐÚNG: "Nguyên tố Hydro H₂ có tính chất đặc biệt (TitleContent)" - Làm thế này!

   VÍ DỤ CẤU TRÚC ĐÚNG:
   I. Khái niệm nguyên tố (TitleName) ← Đây là tên mục lớn
   Nguyên tố hóa học là tập hợp các nguyên tử... (TitleContent) ← Đây là nội dung mục lớn

   1. Định nghĩa (SubtitleName) ← Đây là tên mục nhỏ trong mục lớn
   Nguyên tố được định nghĩa là... (SubtitleContent) ← Đây là nội dung mục nhỏ

   2. Tính chất (SubtitleName) ← Đây là tên mục nhỏ khác
   Các tính chất của nguyên tố bao gồm... (SubtitleContent) ← Đây là nội dung mục nhỏ khác

   VÍ DỤ SAI THƯỜNG GẶP:
   ❌ "Số hiệu nguyên tử (Z) là số proton... (SubtitleName)" - SAI! Đây là nội dung, phải dùng SubtitleContent
   ✅ "Số hiệu nguyên tử (SubtitleName)" và "Số hiệu nguyên tử (Z) là số proton... (SubtitleContent)" - ĐÚNG!

5. SLIDE SUMMARIES:
   Cuối mỗi phần logic của presentation, thêm slide summary với SỐ LƯỢNG RÕ RÀNG:
   === SLIDE [Số] SUMMARY ===
   Placeholders: [Số lượng]x[PlaceholderType], [Số lượng]x[PlaceholderType], ...
   Ví dụ: 1xLessonName, 1xLessonDescription, 1xCreatedDate, 2xTitleName, 3xTitleContent
   ===========================

YÊU CẦU OUTPUT:
Tạo nội dung thuyết trình TEXT THUẦN TÚY với annotation rõ ràng, theo đúng format trên.
BẮT BUỘC có slide summaries để hỗ trợ việc chọn slide template phù hợp.

VÍ DỤ MINH HỌA CẤU TRÚC ĐÚNG:

SLIDE 1:
Bài 1: Cấu hình electron (LessonName)
Bài này cho chúng ta biết được cấu hình electron trong nguyên tử và phân tử (LessonDescription)
Ngày thuyết trình: 12-07-2025 (CreatedDate)

=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================

SLIDE 2:
I. Khái niệm cấu hình electron (TitleName)
Cấu hình electron là cách sắp xếp các electron trong các orbital của nguyên tử. Cấu hình này quyết định tính chất hóa học của nguyên tố và khả năng tạo liên kết. (TitleContent)

=== SLIDE 2 SUMMARY ===
Placeholders: 1xTitleName, 1xTitleContent
===========================

SLIDE 3:
II. Các quy tắc sắp xếp electron (TitleName)

1. Quy tắc Aufbau (SubtitleName)
Electron điền vào orbital có mức năng lượng thấp trước, sau đó mới điền vào orbital có mức năng lượng cao hơn. (SubtitleContent)

2. Nguyên lý Pauli (SubtitleName)
Mỗi orbital chứa tối đa 2 electron và chúng phải có spin ngược chiều nhau. (SubtitleContent)

=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 2xSubtitleName, 2xSubtitleContent
===========================

SLIDE 4:
Hình ảnh minh họa: Sơ đồ cấu hình electron (ImageName)
Sơ đồ thể hiện cách electron được sắp xếp trong các orbital 1s, 2s, 2p theo thứ tự năng lượng tăng dần (ImageContent)

=== SLIDE 4 SUMMARY ===
Placeholders: 1xImageName, 1xImageContent
===========================

QUY TẮC VIẾT:
- LUÔN có annotation (PlaceholderType) sau mỗi nội dung
- Nội dung đầy đủ, chi tiết. Không được bỏ xót bất kì kiến thức nào trong bài học
- TẠMTHỜI KHÔNG sử dụng BulletItem - chỉ dùng 9 placeholder types còn lại
- PHÂN BIỆT RÕ RÀNG CẤU TRÚC PHÂN CẤP:
  * TitleName: CHỈ là tiêu đề mục lớn (I., II., III.)
  * TitleContent: Nội dung giải thích của mục lớn
  * SubtitleName: CHỈ là tiêu đề mục nhỏ (1., 2., 3.) bên trong mục lớn
  * SubtitleContent: Nội dung giải thích của mục nhỏ
- Ký hiệu khoa học chính xác: H₂O, CO₂, x², √x, π, α, β
- Logic trình bày từ tổng quan đến chi tiết
- Sử dụng ngày hiện tại cho CreatedDate
- CHỈ TẠO TEXT THUẦN TÚY - không JSON, không format phức tạp
- BẮT BUỘC có slide summaries với SỐ LƯỢNG RÕ RÀNG (ví dụ: 2xTitleName, 3xSubtitleContent)
"""

        return prompt

    async def _parse_and_map_content_to_template(
        self,
        annotated_content: str,
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Xử lý bằng code: Parse annotated content và map vào template

        Args:
            annotated_content: Nội dung có annotation từ AI
            analyzed_template: Template đã phân tích

        Returns:
            Dict chứa slides đã map content
        """
        try:
            logger.info("🔧 Starting code-based content parsing and mapping...")

            # Bước 1: Parse annotated content
            parsed_content = self._parse_annotated_content(annotated_content)
            if not parsed_content:
                return {
                    "success": False,
                    "error": "Failed to parse annotated content"
                }

            # Bước 2: Map content to template slides
            mapped_slides = await self._map_parsed_content_to_slides(
                parsed_content,
                analyzed_template
            )
            if not mapped_slides["success"]:
                return mapped_slides

            return {
                "success": True,
                "slides": mapped_slides["slides"]
            }

        except Exception as e:
            logger.error(f"Error in code-based content mapping: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _parse_annotated_content(self, annotated_content: str) -> Dict[str, Any]:
        """
        Parse annotated content từ AI response

        Args:
            annotated_content: Nội dung có annotation

        Returns:
            Dict chứa parsed content theo placeholder types
        """
        try:
            import re

            logger.info("📝 Parsing annotated content...")

            # Dictionary để lưu parsed content (tạm thời loại bỏ BulletItem)
            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Slide summaries để track slide structure
            slide_summaries = []

            # Pattern để tìm annotation: text (PlaceholderType)
            # Chỉ match các placeholder types hợp lệ (tạm thời loại bỏ BulletItem)
            valid_placeholders = '|'.join([
                'LessonName', 'LessonDescription', 'CreatedDate',
                'TitleName', 'TitleContent', 'SubtitleName', 'SubtitleContent',
                'ImageName', 'ImageContent'
            ])
            annotation_pattern = rf'(.+?)\s*\(({valid_placeholders})\)'

            # Pattern để tìm slide summaries với format số lượng
            summary_pattern = r'=== SLIDE (\d+) SUMMARY ===\s*Placeholders:\s*([^=]+)\s*==='

            lines = annotated_content.split('\n')
            current_slide_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Check for slide summary
                summary_match = re.search(summary_pattern, line + '\n' + '\n'.join(lines[lines.index(line):lines.index(line)+3]))
                if summary_match:
                    slide_num = int(summary_match.group(1))
                    placeholder_text = summary_match.group(2).strip()

                    # Parse placeholder format: "2xTitleName, 3xSubtitleContent" hoặc "TitleName, SubtitleContent"
                    placeholders = []
                    placeholder_counts = {}

                    for item in placeholder_text.split(','):
                        item = item.strip()
                        if 'x' in item:
                            # Format: "2xTitleName"
                            count_str, placeholder_type = item.split('x', 1)
                            try:
                                count = int(count_str)
                                placeholders.append(placeholder_type.strip())
                                placeholder_counts[placeholder_type.strip()] = count
                            except ValueError:
                                # Fallback nếu không parse được số
                                placeholders.append(item)
                                placeholder_counts[item] = 1
                        else:
                            # Format cũ: "TitleName"
                            placeholders.append(item)
                            placeholder_counts[item] = 1

                    slide_summaries.append({
                        "slide_number": slide_num,
                        "placeholders": placeholders,
                        "placeholder_counts": placeholder_counts,
                        "content": current_slide_content.copy()
                    })
                    current_slide_content = []
                    continue

                # Find annotation matches
                matches = re.findall(annotation_pattern, line)
                for match in matches:
                    content = match[0].strip()
                    placeholder_type = match[1].strip()

                    if placeholder_type in parsed_data:
                        parsed_data[placeholder_type].append({
                            "content": content,
                            "original_line": line
                        })
                        current_slide_content.append({
                            "type": placeholder_type,
                            "content": content
                        })
                        logger.debug(f"✅ Parsed {placeholder_type}: {content[:50]}...")
                    else:
                        logger.warning(f"❌ Unknown placeholder type: {placeholder_type}")

            # If no slide summaries found, create default structure
            if not slide_summaries and any(parsed_data.values()):
                logger.info("No slide summaries found, creating default structure...")
                slide_summaries = [{"slide_number": 1, "placeholders": list(parsed_data.keys()), "content": current_slide_content}]

            result = {
                "parsed_data": parsed_data,
                "slide_summaries": slide_summaries,
                "total_items": sum(len(items) for items in parsed_data.values())
            }

            logger.info(f"✅ Parsing completed: {result['total_items']} items found, {len(slide_summaries)} slides")
            return result

        except Exception as e:
            logger.error(f"Error parsing annotated content: {e}")
            return None

    async def _map_parsed_content_to_slides(
        self,
        parsed_content: Dict[str, Any],
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Map parsed content vào template slides

        Args:
            parsed_content: Content đã parse
            analyzed_template: Template đã phân tích

        Returns:
            Dict chứa mapped slides
        """
        try:
            logger.info("🎯 Mapping parsed content to template slides...")

            template_slides = analyzed_template.get("slides", [])
            parsed_data = parsed_content.get("parsed_data", {})

            mapped_slides = []
            content_index = {key: 0 for key in parsed_data.keys()}  # Track content usage

            slide_summaries = parsed_content.get("slide_summaries", [])

            # Ưu tiên sử dụng slide summaries nếu có
            if slide_summaries:
                logger.info(f"🎯 Using slide summaries for EXACT slide selection: {len(slide_summaries)} summaries found")

                used_slide_ids = set()
                duplicate_counter = 1  # Counter for creating unique duplicate slide IDs

                # Xử lý TẤT CẢ slide summaries theo ĐÚNG THỨ TỰ từ LLM
                for i, summary in enumerate(slide_summaries):
                    slide_num = summary.get("slide_number", i+1)
                    required_placeholders = summary.get("placeholders", [])
                    required_counts = summary.get("placeholder_counts", {})

                    logger.info(f"🔍 Processing slide {slide_num} with exact requirements:")
                    logger.info(f"   Placeholders: {required_placeholders}")
                    logger.info(f"   Counts: {required_counts}")

                    # Tìm slide có placeholder CHÍNH XÁC (cho phép reuse)
                    exact_slide = self._find_exact_matching_slide(
                        required_placeholders,
                        required_counts,
                        template_slides,
                        used_slide_ids
                    )

                    if exact_slide:
                        logger.info(f"✅ Found EXACT slide for summary {slide_num}: {exact_slide['slideId']}")

                        mapped_slide = await self._create_mapped_slide(
                            exact_slide,
                            parsed_data,
                            content_index
                        )

                        if mapped_slide:
                            # Thêm slide_order để giữ đúng thứ tự từ LLM
                            mapped_slide["slide_order"] = slide_num
                            mapped_slides.append(mapped_slide)
                            used_slide_ids.add(exact_slide['slideId'])
                            logger.info(f"✅ Successfully mapped slide {slide_num}: {exact_slide['slideId']}")
                            logger.info(f"📊 Elements mapped: {len(mapped_slide.get('elements', []))}")
                        else:
                            logger.warning(f"❌ Failed to create mapped slide for summary {slide_num}: {exact_slide['slideId']}")
                    else:
                        # Không tìm thấy slide phù hợp -> Tạo duplicate slide
                        logger.info(f"🔄 No matching slide found for summary {slide_num}, creating duplicate...")

                        duplicate_slide = self._create_duplicate_slide_template(
                            template_slides,
                            duplicate_counter
                        )

                        if duplicate_slide:
                            logger.info(f"✅ Created duplicate slide: {duplicate_slide['slideId']}")

                            mapped_slide = await self._create_mapped_slide(
                                duplicate_slide,
                                parsed_data,
                                content_index
                            )

                            if mapped_slide:
                                # Thêm slide_order và mark as new slide
                                mapped_slide["slide_order"] = slide_num
                                mapped_slide["action"] = "create"  # Mark for creation
                                mapped_slide["baseSlideId"] = duplicate_slide.get("baseSlideId")
                                mapped_slides.append(mapped_slide)
                                duplicate_counter += 1
                                logger.info(f"✅ Successfully mapped duplicate slide {slide_num}: {duplicate_slide['slideId']}")
                                logger.info(f"📊 Elements mapped: {len(mapped_slide.get('elements', []))}")
                            else:
                                logger.warning(f"❌ Failed to create mapped slide for duplicate {slide_num}")
                        else:
                            logger.error(f"❌ Failed to create duplicate slide for summary {slide_num}")

                # Sắp xếp slides theo đúng thứ tự từ LLM
                mapped_slides.sort(key=lambda x: x.get("slide_order", 999))

                logger.info(f"🎯 Processed {len(slide_summaries)} summaries, mapped {len(mapped_slides)} slides in correct order")
            else:
                # Fallback: Tìm slide tốt nhất dựa trên tất cả content có sẵn
                logger.info("⚠️ No slide summaries found, using fallback method")

                all_available_placeholders = [
                    placeholder_type for placeholder_type, content_list in parsed_data.items()
                    if content_list and content_index[placeholder_type] < len(content_list)
                ]

                if all_available_placeholders:
                    logger.info(f"🔍 Available placeholders: {all_available_placeholders}")

                    best_slide = self._find_best_matching_slide(
                        all_available_placeholders,
                        template_slides,
                        set()
                    )

                    if best_slide:
                        logger.info(f"✅ Selected fallback slide: {best_slide['slideId']}")

                        mapped_slide = await self._create_mapped_slide(
                            best_slide,
                            parsed_data,
                            content_index
                        )

                        if mapped_slide:
                            mapped_slides.append(mapped_slide)
                            logger.info(f"✅ Successfully mapped fallback slide: {best_slide['slideId']}")
                            logger.info(f"📊 Elements mapped: {len(mapped_slide.get('elements', []))}")
                        else:
                            logger.warning(f"❌ Failed to create mapped slide for: {best_slide['slideId']}")
                    else:
                        logger.warning("❌ No compatible slide found for available content")
                else:
                    logger.warning("❌ No content available for mapping")

            logger.info(f"✅ Mapping completed: {len(mapped_slides)} slides mapped")
            return {
                "success": True,
                "slides": mapped_slides
            }

        except Exception as e:
            logger.error(f"Error mapping content to slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _find_exact_matching_slide(
        self,
        required_placeholders: List[str],
        required_counts: Dict[str, int],
        template_slides: List[Dict[str, Any]],
        used_slide_ids: set
    ) -> Optional[Dict[str, Any]]:
        """
        Tìm slide template có placeholder CHÍNH XÁC với required placeholders
        CHO PHÉP REUSE slides đã sử dụng

        Args:
            required_placeholders: List placeholder types cần thiết
            required_counts: Dict số lượng từng placeholder type
            template_slides: List template slides available
            used_slide_ids: Set slide IDs đã sử dụng (chỉ để tracking, không skip)

        Returns:
            Dict slide template match chính xác hoặc None
        """
        try:
            # Tìm slide match chính xác, KHÔNG skip used slides (cho phép reuse)
            for slide in template_slides:
                slide_id = slide.get("slideId")

                # Get placeholder types and counts in this slide
                slide_elements = slide.get("elements", [])
                slide_placeholder_counts = {}

                for elem in slide_elements:
                    placeholder_type = elem.get("Type")
                    if placeholder_type in slide_placeholder_counts:
                        slide_placeholder_counts[placeholder_type] += 1
                    else:
                        slide_placeholder_counts[placeholder_type] = 1

                # Check for EXACT match: same placeholder types and same counts
                required_set = set(required_placeholders)
                slide_set = set(slide_placeholder_counts.keys())

                # Must have exactly the same placeholder types
                if required_set == slide_set:
                    # Check if counts match
                    counts_match = True
                    for placeholder_type in required_placeholders:
                        required_count = required_counts.get(placeholder_type, 1)
                        slide_count = slide_placeholder_counts.get(placeholder_type, 0)

                        if required_count != slide_count:
                            counts_match = False
                            break

                    if counts_match:
                        reuse_status = "REUSED" if slide_id in used_slide_ids else "FIRST_USE"
                        logger.info(f"✅ Found EXACT matching slide ({reuse_status}): {slide_id}")
                        logger.info(f"   Required: {required_counts}")
                        logger.info(f"   Slide has: {slide_placeholder_counts}")
                        return slide
                    else:
                        logger.debug(f"❌ Slide {slide_id}: placeholder types match but counts differ")
                        logger.debug(f"   Required: {required_counts}")
                        logger.debug(f"   Slide has: {slide_placeholder_counts}")
                else:
                    logger.debug(f"❌ Slide {slide_id}: placeholder types don't match")
                    logger.debug(f"   Required: {required_set}")
                    logger.debug(f"   Slide has: {slide_set}")

            logger.warning(f"❌ No EXACT matching slide found for: {required_counts}")
            return None

        except Exception as e:
            logger.error(f"Error finding exact matching slide: {e}")
            return None

    def _create_duplicate_slide_template(
        self,
        template_slides: List[Dict[str, Any]],
        slide_index: int
    ) -> Optional[Dict[str, Any]]:
        """
        Tạo slide template duplicate từ slide đầu tiên (hoặc slide phù hợp nhất)

        Args:
            template_slides: List template slides available
            slide_index: Index để tạo unique slideId

        Returns:
            Dict slide template duplicate với slideId mới
        """
        try:
            if not template_slides:
                logger.error("❌ No template slides available for duplication")
                return None

            # Sử dụng slide đầu tiên làm base template
            base_slide = template_slides[0]
            base_slide_id = base_slide.get("slideId")

            if not base_slide_id:
                logger.error("❌ Base slide has no slideId")
                return None

            # Tạo slideId mới duy nhất
            new_slide_id = f"new_slide_{slide_index}"

            # Tạo duplicate slide với slideId mới
            duplicate_slide = {
                "slideId": new_slide_id,
                "elements": [],
                "description": base_slide.get("description", "Duplicated slide"),
                "placeholder_counts": base_slide.get("placeholder_counts", {}),
                "baseSlideId": base_slide_id,  # Track base slide for duplication
                "is_duplicate": True  # Mark as duplicate
            }

            # Copy elements với objectId mới
            base_elements = base_slide.get("elements", [])
            for i, element in enumerate(base_elements):
                new_object_id = f"{new_slide_id}_element_{i}"
                duplicate_element = {
                    "objectId": new_object_id,
                    "text": None,  # Will be filled with content later
                    "Type": element.get("Type"),
                    "max_length": element.get("max_length", 1000),
                    "baseObjectId": element.get("objectId")  # Track original element
                }
                duplicate_slide["elements"].append(duplicate_element)

            logger.info(f"✅ Created duplicate slide: {new_slide_id} based on {base_slide_id}")
            logger.info(f"   Elements: {len(duplicate_slide['elements'])}")
            logger.info(f"   Placeholder counts: {duplicate_slide['placeholder_counts']}")

            return duplicate_slide

        except Exception as e:
            logger.error(f"Error creating duplicate slide template: {e}")
            return None

    async def _create_mapped_slide(
        self,
        template_slide: Dict[str, Any],
        parsed_data: Dict[str, List[Dict[str, Any]]],
        content_index: Dict[str, int]
    ) -> Optional[Dict[str, Any]]:
        """
        Tạo mapped slide từ template và parsed content
        Hỗ trợ cả template slides và duplicate slides

        Args:
            template_slide: Template slide (có thể là duplicate)
            parsed_data: Parsed content data
            content_index: Index tracking cho content usage

        Returns:
            Dict mapped slide hoặc None
        """
        try:
            slide_id = template_slide.get("slideId")
            template_elements = template_slide.get("elements", [])
            is_duplicate = template_slide.get("is_duplicate", False)

            mapped_elements = []

            for element in template_elements:
                object_id = element.get("objectId")
                placeholder_type = element.get("Type")
                max_length = element.get("max_length", 1000)

                # Get content for this placeholder type
                content_list = parsed_data.get(placeholder_type, [])
                current_index = content_index.get(placeholder_type, 0)

                if current_index < len(content_list):
                    content_item = content_list[current_index]
                    raw_content = content_item.get("content", "")

                    # Check max_length and handle if needed
                    final_content = await self._handle_max_length_content(
                        raw_content,
                        max_length,
                        placeholder_type
                    )

                    mapped_element = {
                        "objectId": object_id,
                        "text": final_content,
                        "Type": placeholder_type,
                        "max_length": max_length
                    }

                    # For duplicate slides, add base object ID for tracking
                    if is_duplicate:
                        mapped_element["baseObjectId"] = element.get("baseObjectId")

                    mapped_elements.append(mapped_element)
                    content_index[placeholder_type] = current_index + 1

                    logger.debug(f"✅ Mapped {placeholder_type}: {final_content[:50]}...")
                else:
                    logger.warning(f"❌ No content available for {placeholder_type} in slide {slide_id}")

            if mapped_elements:
                mapped_slide = {
                    "slideId": slide_id,
                    "elements": mapped_elements
                }

                # Add additional info for duplicate slides
                if is_duplicate:
                    mapped_slide["is_duplicate"] = True
                    mapped_slide["baseSlideId"] = template_slide.get("baseSlideId")

                return mapped_slide
            else:
                logger.warning(f"❌ No elements mapped for slide {slide_id}")
                return None

        except Exception as e:
            logger.error(f"Error creating mapped slide: {e}")
            return None

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str
    ) -> str:
        """
        Xử lý content vượt quá max_length bằng cách gọi LLM để viết lại

        Args:
            content: Nội dung gốc
            max_length: Giới hạn độ dài
            placeholder_type: Loại placeholder

        Returns:
            str: Nội dung đã xử lý (có thể đã được viết lại)
        """
        try:
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content exceeds max_length ({len(content)} > {max_length}) for {placeholder_type}")
            logger.info("🤖 Requesting LLM to rewrite content...")

            # Tạo prompt để LLM viết lại content
            rewrite_prompt = f"""
Bạn cần viết lại nội dung sau để phù hợp với giới hạn độ dài, nhưng vẫn giữ nguyên ý nghĩa và thông tin quan trọng.

NỘI DUNG GỐC:
{content}

YÊU CẦU:
- Độ dài tối đa: {max_length} ký tự
- Giữ nguyên ý nghĩa và thông tin quan trọng
- Phù hợp với loại placeholder: {placeholder_type}
- Ngôn ngữ rõ ràng, súc tích
- Ký hiệu khoa học chính xác nếu có

CHỈ TRẢ VỀ NỘI DUNG ĐÃ VIẾT LẠI, KHÔNG CÓ GIẢI THÍCH THÊM.
"""

            # Gọi LLM để viết lại
            llm_result = await self.llm_service.generate_content(
                prompt=rewrite_prompt,
                temperature=0.1,
                max_tokens=2000
            )

            if llm_result["success"] and llm_result.get("text"):
                rewritten_content = llm_result["text"].strip()

                # Kiểm tra độ dài sau khi viết lại
                if len(rewritten_content) <= max_length:
                    logger.info(f"✅ Content rewritten successfully: {len(rewritten_content)} chars")
                    return rewritten_content
                else:
                    # Nếu vẫn dài, cắt ngắn
                    logger.warning(f"⚠️ Rewritten content still too long, truncating...")
                    return rewritten_content[:max_length-3] + "..."
            else:
                logger.error(f"❌ LLM rewrite failed: {llm_result.get('error', 'Unknown error')}")
                # Fallback: cắt ngắn content gốc
                return content[:max_length-3] + "..."

        except Exception as e:
            logger.error(f"Error handling max_length content: {e}")
            # Fallback: cắt ngắn content gốc
            return content[:max_length-3] + "..."





    def _filter_used_slides(self, mapped_slides: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Lọc và chỉ giữ slides được sử dụng, xóa slides thừa
        Hỗ trợ cả template slides và duplicate slides

        Args:
            mapped_slides: Slides đã map content từ AI

        Returns:
            List slides đã lọc (chỉ giữ slides được sử dụng)
        """
        try:
            used_slide_ids = set()
            final_slides = []

            # Collect used slide IDs
            for slide in mapped_slides:
                slide_id = slide.get("slideId")
                if slide_id and slide.get("elements"):
                    used_slide_ids.add(slide_id)

            logger.info(f"Used slide IDs: {list(used_slide_ids)}")

            # Convert mapped slides to final format (compatible with Google Slides API)
            # Maintain slide order from LLM presentation content
            for slide in mapped_slides:
                slide_id = slide.get("slideId")
                elements = slide.get("elements", [])
                is_duplicate = slide.get("is_duplicate", False)
                action = slide.get("action", "update")  # Default to update for existing slides
                slide_order = slide.get("slide_order", 999)  # Get LLM order

                if slide_id and elements:
                    # Create updates dictionary for Google Slides API
                    updates = {}
                    for element in elements:
                        object_id = element.get("objectId")
                        text = element.get("text")
                        if object_id and text is not None:
                            updates[object_id] = text

                    if updates:
                        final_slide = {
                            "slideId": slide_id,
                            "action": action,
                            "updates": updates,
                            "slide_order": slide_order  # Preserve LLM order
                        }

                        # Add additional info for duplicate slides
                        if is_duplicate or slide_id.startswith('new_slide_'):
                            final_slide["action"] = "create"
                            final_slide["baseSlideId"] = slide.get("baseSlideId")

                        final_slides.append(final_slide)

            # Sort final slides by LLM order to maintain correct sequence
            final_slides.sort(key=lambda x: x.get("slide_order", 999))

            logger.info(f"Final slides count: {len(final_slides)}")
            logger.info(f"   Update slides: {len([s for s in final_slides if s.get('action') == 'update'])}")
            logger.info(f"   Create slides: {len([s for s in final_slides if s.get('action') == 'create'])}")

            return final_slides

        except Exception as e:
            logger.error(f"Error filtering used slides: {e}")
            return mapped_slides  # Return original as fallback




# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()

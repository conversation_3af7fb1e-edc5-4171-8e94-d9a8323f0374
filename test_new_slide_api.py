#!/usr/bin/env python3
"""
Test script for new slide generation API with placeholder enum support
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_generate_slides_new():
    """Test the updated generate-slides API with new placeholder enum workflow"""
    print("🔄 Testing new generate-slides API...")
    
    # Test data
    lesson_id = "lesson_test_001"
    template_id = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"  # Example template ID
    
    payload = {
        "lesson_id": lesson_id,
        "template_id": template_id,
        "config_prompt": "Tạo slide với phong cách sinh động, sử dụng placeholder enum mới",
        "presentation_title": "Test Bài học với Placeholder Enum"
    }
    
    print(f"📤 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    try:
        # Call API
        response = requests.post(f"{BASE_URL}/slides/generate-slides", json=payload, timeout=120)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"📊 Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Check if we have the expected fields
            if result.get("success"):
                print(f"🎯 Presentation ID: {result.get('presentation_id')}")
                print(f"🔗 Web view link: {result.get('web_view_link')}")
                print(f"📄 Slides created: {result.get('slides_created')}")
                
                # Check for debug info from new workflow
                if "presentation_content" in result:
                    print(f"🧠 AI generated presentation content structure")
                if "analyzed_template" in result:
                    print(f"🔍 Template analysis completed")
                    
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out (this is normal for long-running operations)")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_health_check():
    """Test health check endpoint"""
    print("🔄 Testing health check...")
    
    try:
        response = requests.get(f"{BASE_URL}/slides/health")
        print(f"📥 Health check status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Health check result:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ Health check failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Health check exception: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting new slide generation API tests...")
    print("=" * 50)
    
    # Test 1: Health check
    health_ok = test_health_check()
    print()
    
    if not health_ok:
        print("❌ Health check failed, skipping other tests")
        return
    
    # Test 2: Generate slides with new workflow
    slides_ok = test_generate_slides_new()
    print()
    
    # Summary
    print("📋 Test Summary:")
    print(f"  Health Check: {'✅' if health_ok else '❌'}")
    print(f"  Generate Slides: {'✅' if slides_ok else '❌'}")
    
    if health_ok and slides_ok:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed")

if __name__ == "__main__":
    main()
